#!/usr/bin/env python3
"""
Quick Test for Milestone 2: Browser Automation
Fast validation of the complete system with real user data
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

# Real Pro User Data from Supabase
USER_ID = "69d967d5-0b7b-402b-ae1b-711d9b74eef4"
CONFIG_ID = "b39270c0-feb8-4c99-b6d2-9ee224edd57e"
USER_EMAIL = "<EMAIL>"

async def quick_test():
    """Run a quick comprehensive test"""
    print("🚀 MILESTONE 2 QUICK TEST")
    print("=" * 50)
    print(f"User: {USER_EMAIL}")
    print(f"User ID: {USER_ID}")
    print(f"Config ID: {CONFIG_ID}")
    print("=" * 50)
    
    base_url = "http://localhost:8001"
    
    async with httpx.AsyncClient(timeout=300.0) as client:
        
        # 1. Test service health
        print("\n1️⃣ Testing Service Health...")
        try:
            response = await client.get(f"{base_url}/health/ready")
            if response.status_code == 200:
                print("✅ Service is healthy")
            else:
                print(f"❌ Service health failed: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Cannot connect to service: {e}")
            return
            
        # 2. Test detailed health
        print("\n2️⃣ Testing Detailed Components...")
        try:
            response = await client.get(f"{base_url}/api/v1/test/health-detailed")
            if response.status_code == 200:
                health = response.json()
                print(f"✅ Browser Use: {health.get('browser_use_available', False)}")
                print(f"✅ LangGraph: {health.get('langgraph_available', False)}")
                print(f"✅ Playwright: {health.get('playwright_available', False)}")
            else:
                print(f"⚠️ Detailed health check failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Detailed health error: {e}")
            
        # 3. Test the main Messi task
        print("\n3️⃣ Testing Main Task: Messi Football Information")
        print("-" * 40)
        
        task = "when does messi play his next match and did he score in his last?"
        
        request_data = {
            "user_id": USER_ID,
            "config_id": CONFIG_ID,
            "user_tier": "professional",
            "task": task,
            "workflow_type": "hierarchical",
            "routing_strategy": "intelligent_role",
            "max_roles": 3,
            "max_steps": 12,
            "timeout_seconds": 240,
            "enable_memory": True,
            "enable_screenshots": True,
            "headless": True,
            "verify_results": True,
            "api_keys": [
                {
                    "id": "7f4583a5-6f78-4d3c-99a7-bf8c54ce49e5",
                    "provider": "google",
                    "model": "google/gemini-2.0-flash-001",
                    "label": "gem flash 2"
                },
                {
                    "id": "********-9efb-4a88-8eba-490625742d16",
                    "provider": "openrouter",
                    "model": "microsoft/phi-4-reasoning:free",
                    "label": "llama"
                }
            ]
        }
        
        print(f"📝 Task: {task}")
        print("🔄 Executing browser automation...")
        
        start_time = time.time()
        
        try:
            response = await client.post(
                f"{base_url}/api/v1/browser/execute",
                json=request_data
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"\n📊 RESULTS:")
                print(f"✅ Success: {result.get('success', False)}")
                print(f"🆔 Task ID: {result.get('task_id', 'unknown')}")
                print(f"⏱️ Execution Time: {execution_time:.2f}s")
                
                # Show metadata
                metadata = result.get("execution_metadata", {})
                if metadata:
                    print(f"🔢 Steps: {metadata.get('steps_completed', 0)}")
                    print(f"👥 Roles: {', '.join(metadata.get('roles_used', []))}")
                    print(f"🌐 Pages: {len(metadata.get('pages_visited', []))}")
                    
                    # Show some pages visited
                    pages = metadata.get('pages_visited', [])
                    if pages:
                        print(f"\n🌐 Pages Visited:")
                        for i, page in enumerate(pages[:3], 1):
                            print(f"   {i}. {page.get('url', 'Unknown')}")
                        if len(pages) > 3:
                            print(f"   ... and {len(pages) - 3} more")
                
                # Show result
                final_result = result.get("result", "")
                print(f"\n📋 Result ({len(final_result)} chars):")
                if len(final_result) > 300:
                    print(f"{final_result[:300]}...")
                else:
                    print(final_result)
                    
                # Show any errors
                errors = result.get("errors", [])
                if errors:
                    print(f"\n⚠️ Errors:")
                    for error in errors:
                        print(f"   - {error}")
                        
                print(f"\n🎉 MILESTONE 2 TEST {'PASSED' if result.get('success') else 'FAILED'}!")
                
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ Test execution failed after {execution_time:.2f}s: {e}")
            
        # 4. Test a simpler task for comparison
        print("\n4️⃣ Testing Simple Task: Weather")
        print("-" * 40)
        
        simple_task = "what's the weather like in London today?"
        
        simple_request = {
            "user_id": USER_ID,
            "config_id": CONFIG_ID,
            "user_tier": "professional",
            "task": simple_task,
            "workflow_type": "sequential",
            "routing_strategy": "intelligent_role",
            "max_roles": 2,
            "max_steps": 6,
            "timeout_seconds": 120,
            "enable_memory": True,
            "enable_screenshots": False,
            "headless": True,
            "verify_results": True
        }
        
        print(f"📝 Task: {simple_task}")
        print("🔄 Executing...")
        
        start_time = time.time()
        
        try:
            response = await client.post(
                f"{base_url}/api/v1/browser/execute",
                json=simple_request
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Simple task {'PASSED' if result.get('success') else 'FAILED'} in {execution_time:.2f}s")
                
                final_result = result.get("result", "")
                if final_result:
                    print(f"📋 Result: {final_result[:150]}...")
            else:
                print(f"❌ Simple task failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Simple task error: {e}")
            
    print("\n" + "=" * 50)
    print("🏁 MILESTONE 2 QUICK TEST COMPLETED")
    print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(quick_test())
