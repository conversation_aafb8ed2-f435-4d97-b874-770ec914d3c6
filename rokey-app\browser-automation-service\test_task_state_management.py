"""
Test script for Task State Management System
Tests task tracking, progress monitoring, and context management
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from app.services.task_state_manager import (
    task_state_manager, TaskContext, TaskPriority, TaskType
)
from app.services.task_progress_tracker import (
    task_progress_tracker, ProgressEventType, WorkflowProgress
)
from app.services.task_context_manager import (
    task_context_manager, ContextScope, AgentContext
)
from app.models.browser_automation import TaskStatus


async def test_task_state_manager():
    """Test the task state manager functionality"""
    
    print("🧪 Testing Task State Manager")
    print("=" * 50)
    
    try:
        # Test 1: Task Creation
        print("\n1️⃣ Testing Task Creation...")
        
        # Create task context
        context = TaskContext(
            session_id="test_session_001",
            user_id="test_user_001",
            workflow_id="test_workflow_001",
            agent_role="browser_executor",
            timeout_seconds=60
        )
        
        # Create tasks with different priorities
        task_ids = []
        
        for i in range(3):
            task_id = await task_state_manager.create_task(
                task_description=f"Test task {i+1}: Navigate to example.com",
                context=context,
                priority=TaskPriority.HIGH if i == 0 else TaskPriority.MEDIUM,
                task_type=TaskType.NAVIGATION,
                metadata={"test_data": f"task_{i+1}"}
            )
            task_ids.append(task_id)
        
        print("✅ Task creation completed")
        print(f"   Created {len(task_ids)} tasks")
        
        # Test 2: Task Status Updates
        print("\n2️⃣ Testing Task Status Updates...")
        
        # Update first task to in progress
        success = await task_state_manager.update_task_status(
            task_id=task_ids[0],
            status=TaskStatus.IN_PROGRESS,
            progress_data={"step": "starting_browser"}
        )
        print(f"   Task {task_ids[0][:8]} set to IN_PROGRESS: {'✅' if success else '❌'}")
        
        # Complete first task
        success = await task_state_manager.update_task_status(
            task_id=task_ids[0],
            status=TaskStatus.COMPLETED,
            result={"url": "https://example.com", "title": "Example Domain"}
        )
        print(f"   Task {task_ids[0][:8]} completed: {'✅' if success else '❌'}")
        
        # Fail second task
        success = await task_state_manager.update_task_status(
            task_id=task_ids[1],
            status=TaskStatus.FAILED,
            error_message="Navigation timeout"
        )
        print(f"   Task {task_ids[1][:8]} failed: {'✅' if success else '❌'}")
        
        # Test 3: Get Ready Tasks
        print("\n3️⃣ Testing Ready Tasks Retrieval...")
        
        ready_tasks = await task_state_manager.get_ready_tasks(
            workflow_id="test_workflow_001",
            max_tasks=5
        )
        
        print("✅ Ready tasks retrieved")
        print(f"   Ready tasks count: {len(ready_tasks)}")
        for task in ready_tasks:
            print(f"   - {task.id[:8]}: {task.task[:50]}...")
        
        # Test 4: Task Dependencies
        print("\n4️⃣ Testing Task Dependencies...")
        
        # Create dependent task
        dependent_task_id = await task_state_manager.create_task(
            task_description="Dependent task: Extract data from page",
            context=context,
            priority=TaskPriority.MEDIUM,
            task_type=TaskType.EXTRACTION,
            dependencies=[task_ids[0]]  # Depends on first task
        )
        
        # Try to add circular dependency (should fail)
        circular_success = await task_state_manager.add_task_dependency(
            task_id=task_ids[0],
            dependency_id=dependent_task_id
        )
        
        print("✅ Task dependencies tested")
        print(f"   Dependent task created: {dependent_task_id[:8]}")
        print(f"   Circular dependency prevented: {'✅' if not circular_success else '❌'}")
        
        # Test 5: Workflow Progress
        print("\n5️⃣ Testing Workflow Progress...")
        
        progress = await task_state_manager.get_workflow_progress("test_workflow_001")
        
        print("✅ Workflow progress retrieved")
        print(f"   Total tasks: {progress.get('total_tasks', 0)}")
        print(f"   Completed tasks: {progress.get('completed_tasks', 0)}")
        print(f"   Failed tasks: {progress.get('failed_tasks', 0)}")
        print(f"   Progress: {progress.get('progress_percentage', 0):.1f}%")
        
        # Test 6: Task Retry
        print("\n6️⃣ Testing Task Retry...")
        
        retry_success = await task_state_manager.retry_failed_task(task_ids[1])
        
        print("✅ Task retry tested")
        print(f"   Retry initiated: {'✅' if retry_success else '❌'}")
        
        # Test 7: Execution Statistics
        print("\n7️⃣ Testing Execution Statistics...")
        
        stats = await task_state_manager.get_execution_statistics()
        
        print("✅ Execution statistics retrieved")
        print(f"   Total tasks: {stats.get('total_tasks', 0)}")
        print(f"   Success rate: {stats.get('success_rate', 0):.1f}%")
        print(f"   Average execution time: {stats.get('average_execution_time', 0):.2f}s")
        
    except Exception as e:
        print(f"❌ Task state manager test failed: {e}")


async def test_task_progress_tracker():
    """Test the task progress tracker functionality"""
    
    print("\n📊 Testing Task Progress Tracker")
    print("-" * 40)
    
    try:
        # Test 1: Start Workflow Tracking
        print("\n1️⃣ Testing Workflow Tracking Initialization...")
        
        workflow_id = "test_workflow_progress_001"
        milestones = [
            {"name": "Quarter Complete", "percentage": 25, "description": "25% of tasks completed"},
            {"name": "Half Complete", "percentage": 50, "description": "50% of tasks completed"},
            {"name": "Three Quarters", "percentage": 75, "description": "75% of tasks completed"},
            {"name": "Nearly Done", "percentage": 90, "description": "90% of tasks completed"}
        ]
        
        await task_progress_tracker.start_workflow_tracking(
            workflow_id=workflow_id,
            total_tasks=10,
            milestones=milestones
        )
        
        print("✅ Workflow tracking started")
        print(f"   Workflow ID: {workflow_id}")
        print(f"   Total tasks: 10")
        print(f"   Milestones: {len(milestones)}")
        
        # Test 2: Progress Callback Registration
        print("\n2️⃣ Testing Progress Callbacks...")
        
        progress_updates = []
        
        async def progress_callback(event):
            progress_updates.append({
                "type": event.event_type.value,
                "message": event.message,
                "progress": event.progress_percentage
            })
            print(f"   📈 Progress Update: {event.message} ({event.progress_percentage:.1f}%)")
        
        await task_progress_tracker.register_progress_callback(
            workflow_id=workflow_id,
            callback=progress_callback,
            event_types=[ProgressEventType.TASK_COMPLETED, ProgressEventType.MILESTONE_REACHED]
        )
        
        print("✅ Progress callback registered")
        
        # Test 3: Simulate Task Progress
        print("\n3️⃣ Testing Task Progress Updates...")
        
        # Simulate completing tasks
        for i in range(10):
            task_id = f"task_{i+1}"
            
            # Start task
            await task_progress_tracker.update_task_progress(
                workflow_id=workflow_id,
                task_id=task_id,
                status=TaskStatus.IN_PROGRESS,
                progress_percentage=0,
                message=f"Starting task {i+1}"
            )
            
            # Simulate some work
            await asyncio.sleep(0.1)
            
            # Complete task
            await task_progress_tracker.update_task_progress(
                workflow_id=workflow_id,
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                progress_percentage=100,
                message=f"Completed task {i+1}",
                details={"execution_time": 0.1, "result": f"Task {i+1} result"}
            )
        
        print("✅ Task progress simulation completed")
        print(f"   Progress updates received: {len(progress_updates)}")
        
        # Test 4: Get Workflow Progress
        print("\n4️⃣ Testing Workflow Progress Retrieval...")
        
        final_progress = await task_progress_tracker.get_workflow_progress(workflow_id)
        
        if final_progress:
            print("✅ Final workflow progress retrieved")
            print(f"   Progress: {final_progress.progress_percentage:.1f}%")
            print(f"   Completed tasks: {final_progress.completed_tasks}")
            print(f"   Duration: {(final_progress.current_time - final_progress.start_time).total_seconds():.2f}s")
        else:
            print("❌ Failed to retrieve workflow progress")
        
        # Test 5: Progress Events
        print("\n5️⃣ Testing Progress Events...")
        
        events = await task_progress_tracker.get_progress_events(
            workflow_id=workflow_id,
            limit=20
        )
        
        print("✅ Progress events retrieved")
        print(f"   Total events: {len(events)}")
        
        event_types = {}
        for event in events:
            event_type = event.event_type.value
            event_types[event_type] = event_types.get(event_type, 0) + 1
        
        for event_type, count in event_types.items():
            print(f"   - {event_type}: {count}")
        
        # Test 6: Progress Analytics
        print("\n6️⃣ Testing Progress Analytics...")
        
        analytics = await task_progress_tracker.get_progress_analytics(workflow_id)
        
        print("✅ Progress analytics retrieved")
        print(f"   Workflow ID: {analytics.get('workflow_id')}")
        print(f"   Performance metrics available: {'✅' if 'performance_metrics' in analytics else '❌'}")
        
        # Stop tracking
        await task_progress_tracker.stop_workflow_tracking(workflow_id)
        print("✅ Workflow tracking stopped")
        
    except Exception as e:
        print(f"❌ Task progress tracker test failed: {e}")


async def test_task_context_manager():
    """Test the task context manager functionality"""
    
    print("\n🧠 Testing Task Context Manager")
    print("-" * 40)
    
    try:
        # Test 1: Initialize Context Manager
        print("\n1️⃣ Testing Context Manager Initialization...")
        
        await task_context_manager.initialize()
        print("✅ Context manager initialized")
        
        # Test 2: Set and Get Context
        print("\n2️⃣ Testing Context Storage and Retrieval...")
        
        # Set global context
        await task_context_manager.set_context(
            key="api_base_url",
            value="https://api.example.com",
            scope=ContextScope.GLOBAL,
            scope_id="global"
        )
        
        # Set workflow context
        workflow_id = "test_workflow_context_001"
        await task_context_manager.set_context(
            key="workflow_config",
            value={"max_retries": 3, "timeout": 30},
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id
        )
        
        # Get context with inheritance
        api_url = await task_context_manager.get_context(
            key="api_base_url",
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id,
            inherit=True
        )
        
        workflow_config = await task_context_manager.get_context(
            key="workflow_config",
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id
        )
        
        print("✅ Context storage and retrieval tested")
        print(f"   Global API URL: {api_url}")
        print(f"   Workflow config: {workflow_config}")
        
        # Test 3: Agent Context
        print("\n3️⃣ Testing Agent Context...")
        
        # Create agent context
        agent_context = await task_context_manager.create_agent_context(
            agent_id="test_agent_001",
            agent_role="browser_executor",
            workflow_id=workflow_id,
            session_id="test_session_001"
        )
        
        # Update agent context
        await task_context_manager.update_agent_context(
            agent_id="test_agent_001",
            updates={
                "current_url": "https://example.com",
                "page_title": "Example Domain",
                "execution_step": "data_extraction"
            }
        )
        
        print("✅ Agent context tested")
        print(f"   Agent ID: {agent_context.agent_id}")
        print(f"   Agent role: {agent_context.agent_role}")
        
        # Test 4: Context Sharing
        print("\n4️⃣ Testing Context Sharing Between Agents...")
        
        # Create second agent
        agent_context_2 = await task_context_manager.create_agent_context(
            agent_id="test_agent_002",
            agent_role="verification_agent",
            workflow_id=workflow_id,
            session_id="test_session_002"
        )
        
        # Share context between agents
        sharing_success = await task_context_manager.share_context_between_agents(
            source_agent_id="test_agent_001",
            target_agent_id="test_agent_002",
            context_keys=["current_url", "page_title"]
        )
        
        print("✅ Context sharing tested")
        print(f"   Sharing successful: {'✅' if sharing_success else '❌'}")
        
        # Test 5: Workflow Context
        print("\n5️⃣ Testing Workflow Context Retrieval...")
        
        workflow_context = await task_context_manager.get_workflow_context(workflow_id)
        
        print("✅ Workflow context retrieved")
        print(f"   Context keys: {list(workflow_context.keys())}")
        print(f"   Agents in workflow: {len(workflow_context.get('agents', {}))}")
        
        # Test 6: Context with TTL
        print("\n6️⃣ Testing Context with TTL...")
        
        # Set context with short TTL
        await task_context_manager.set_context(
            key="temporary_data",
            value="This will expire soon",
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id,
            ttl_seconds=2
        )
        
        # Get immediately
        temp_data = await task_context_manager.get_context(
            key="temporary_data",
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id
        )
        print(f"   Temporary data (immediate): {temp_data}")
        
        # Wait for expiration
        await asyncio.sleep(3)
        
        # Try to get after expiration
        expired_data = await task_context_manager.get_context(
            key="temporary_data",
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id,
            default="EXPIRED"
        )
        print(f"   Temporary data (after TTL): {expired_data}")
        
        # Test 7: Context Analytics
        print("\n7️⃣ Testing Context Analytics...")
        
        analytics = await task_context_manager.get_context_analytics()
        
        print("✅ Context analytics retrieved")
        print(f"   Total global context: {analytics.get('total_global_context', 0)}")
        print(f"   Total agent contexts: {analytics.get('total_agent_contexts', 0)}")
        print(f"   Context by scope: {analytics.get('context_by_scope', {})}")
        
        # Test 8: Context Cleanup
        print("\n8️⃣ Testing Context Cleanup...")
        
        cleaned_count = await task_context_manager.cleanup_expired_context()
        
        print("✅ Context cleanup tested")
        print(f"   Expired entries cleaned: {cleaned_count}")
        
    except Exception as e:
        print(f"❌ Task context manager test failed: {e}")


async def test_integration_scenarios():
    """Test integrated task management scenarios"""
    
    print("\n🔗 Testing Integration Scenarios")
    print("-" * 40)
    
    try:
        # Scenario 1: Complete Workflow with All Components
        print("\n1️⃣ Complete Workflow Integration...")
        
        workflow_id = "integration_test_workflow"
        
        # Initialize all components
        await task_context_manager.initialize()
        
        # Start progress tracking
        await task_progress_tracker.start_workflow_tracking(
            workflow_id=workflow_id,
            total_tasks=5,
            milestones=[
                {"name": "Setup Complete", "percentage": 20, "description": "Initial setup done"},
                {"name": "Halfway", "percentage": 50, "description": "Half the work done"},
                {"name": "Almost Done", "percentage": 80, "description": "Nearly finished"}
            ]
        )
        
        # Create workflow context
        await task_context_manager.set_context(
            key="workflow_settings",
            value={"browser": "chrome", "headless": False, "timeout": 30},
            scope=ContextScope.WORKFLOW,
            scope_id=workflow_id
        )
        
        # Create agent context
        agent_context = await task_context_manager.create_agent_context(
            agent_id="integration_agent",
            agent_role="browser_executor",
            workflow_id=workflow_id,
            session_id="integration_session"
        )
        
        # Create tasks with state management
        task_context = TaskContext(
            session_id="integration_session",
            user_id="integration_user",
            workflow_id=workflow_id,
            agent_role="browser_executor"
        )
        
        task_ids = []
        for i in range(5):
            task_id = await task_state_manager.create_task(
                task_description=f"Integration task {i+1}",
                context=task_context,
                priority=TaskPriority.MEDIUM,
                task_type=TaskType.INTERACTION
            )
            task_ids.append(task_id)
        
        # Execute tasks with progress tracking
        for i, task_id in enumerate(task_ids):
            # Update task state
            await task_state_manager.update_task_status(
                task_id=task_id,
                status=TaskStatus.IN_PROGRESS
            )
            
            # Update progress tracker
            await task_progress_tracker.update_task_progress(
                workflow_id=workflow_id,
                task_id=task_id,
                status=TaskStatus.IN_PROGRESS,
                progress_percentage=0,
                message=f"Starting integration task {i+1}"
            )
            
            # Update agent context
            await task_context_manager.update_agent_context(
                agent_id="integration_agent",
                updates={"current_task": f"task_{i+1}", "step": "executing"}
            )
            
            # Simulate work
            await asyncio.sleep(0.1)
            
            # Complete task
            await task_state_manager.update_task_status(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                result=f"Task {i+1} completed successfully"
            )
            
            await task_progress_tracker.update_task_progress(
                workflow_id=workflow_id,
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                progress_percentage=100,
                message=f"Completed integration task {i+1}"
            )
        
        # Get final results
        workflow_progress = await task_state_manager.get_workflow_progress(workflow_id)
        progress_tracker_data = await task_progress_tracker.get_workflow_progress(workflow_id)
        workflow_context = await task_context_manager.get_workflow_context(workflow_id)
        
        print("✅ Complete workflow integration successful")
        print(f"   Tasks completed: {workflow_progress.get('completed_tasks', 0)}/5")
        print(f"   Progress: {progress_tracker_data.progress_percentage:.1f}%")
        print(f"   Context keys: {len(workflow_context)}")
        
        # Cleanup
        await task_progress_tracker.stop_workflow_tracking(workflow_id)
        
        print("\n✅ All integration scenarios completed successfully")
        
    except Exception as e:
        print(f"❌ Integration scenarios test failed: {e}")


if __name__ == "__main__":
    print("🚀 RouKey Task State Management Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    asyncio.run(test_task_state_manager())
    asyncio.run(test_task_progress_tracker())
    asyncio.run(test_task_context_manager())
    asyncio.run(test_integration_scenarios())
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 All task state management tests completed!")
    print("\n📝 Summary:")
    print("   ✅ Task State Manager - Robust task tracking and dependency management")
    print("   ✅ Task Progress Tracker - Real-time progress monitoring and analytics")
    print("   ✅ Task Context Manager - Context preservation across agents")
    print("   ✅ Integration Scenarios - Complete workflow coordination")
