"""
User Configuration Manager
Manages user-specific browser automation configuration settings integrated with Rou<PERSON>ey's custom model setup
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass, field
import json

from app.core.logging import LoggerMixin
from app.services.access_control import access_control_manager, SubscriptionTier


class BrowserAutomationMode(Enum):
    """Browser automation operation modes"""
    DISABLED = "disabled"
    BASIC = "basic"
    ADVANCED = "advanced"
    FULL = "full"


class AutomationTrigger(Enum):
    """Automation trigger types"""
    MANUAL = "manual"
    INTENT_BASED = "intent_based"
    KEYWORD_BASED = "keyword_based"
    ALWAYS = "always"


@dataclass
class BrowserAutomationConfig:
    """Browser automation configuration for a user"""
    enabled: bool = False
    mode: BrowserAutomationMode = BrowserAutomationMode.DISABLED
    trigger: AutomationTrigger = AutomationTrigger.MANUAL
    
    # Feature toggles
    enable_navigation: bool = True
    enable_data_extraction: bool = True
    enable_form_interaction: bool = False
    enable_file_downloads: bool = False
    enable_screenshot_capture: bool = True
    enable_verification: bool = True
    
    # Performance settings
    max_concurrent_sessions: int = 1
    session_timeout_minutes: int = 10
    enable_session_reuse: bool = True
    enable_performance_optimization: bool = True
    
    # Security settings
    allow_external_sites: bool = True
    blocked_domains: List[str] = field(default_factory=list)
    allowed_domains: List[str] = field(default_factory=list)
    enable_content_filtering: bool = False
    
    # Integration settings
    enable_role_routing: bool = True
    preferred_roles: List[str] = field(default_factory=list)
    enable_fallback_strategies: bool = True
    enable_error_recovery: bool = True
    
    # Notification settings
    notify_on_completion: bool = False
    notify_on_errors: bool = True
    include_screenshots_in_results: bool = False
    
    # Advanced settings
    custom_user_agent: Optional[str] = None
    custom_viewport_size: Optional[str] = None
    enable_javascript: bool = True
    enable_images: bool = False
    enable_css: bool = True
    
    # Usage preferences
    auto_save_results: bool = True
    result_retention_days: int = 30
    enable_usage_analytics: bool = True


@dataclass
class UserCustomConfig:
    """User's complete custom configuration including browser automation"""
    user_id: str
    config_name: str
    created_at: datetime
    updated_at: datetime
    
    # Model configuration (existing RouKey functionality)
    model_configs: Dict[str, Any] = field(default_factory=dict)
    routing_strategies: Dict[str, Any] = field(default_factory=dict)
    role_assignments: Dict[str, Any] = field(default_factory=dict)
    
    # Browser automation configuration (new)
    browser_automation: BrowserAutomationConfig = field(default_factory=BrowserAutomationConfig)
    
    # Integration settings
    enable_browser_automation: bool = False
    browser_automation_priority: int = 5  # 1-10 priority for automation vs regular chat
    
    # Metadata
    is_active: bool = True
    subscription_tier: str = "starter"
    last_used: Optional[datetime] = None


class UserConfigManager(LoggerMixin):
    """
    Manages user configuration for browser automation integration with RouKey's custom model setup
    
    Features:
    - Browser automation toggle in custom config UI
    - Tier-based feature availability
    - Integration with existing model configuration
    - Real-time configuration updates
    - Configuration validation and defaults
    - Usage tracking and analytics
    """
    
    def __init__(self):
        self.user_configs: Dict[str, UserCustomConfig] = {}
        self.config_templates: Dict[str, BrowserAutomationConfig] = {}
        self.tier_restrictions: Dict[str, Dict[str, Any]] = {}
        
        # Initialize tier-based restrictions
        self._initialize_tier_restrictions()
        
        # Initialize configuration templates
        self._initialize_config_templates()
        
        self.log_info("User configuration manager initialized")
    
    def _initialize_tier_restrictions(self):
        """Initialize tier-based feature restrictions"""
        self.tier_restrictions = {
            "starter": {
                "max_concurrent_sessions": 1,
                "enable_form_interaction": False,
                "enable_file_downloads": False,
                "enable_advanced_features": False,
                "max_blocked_domains": 5,
                "max_allowed_domains": 10,
                "session_timeout_minutes": 5,
                "available_modes": [BrowserAutomationMode.DISABLED, BrowserAutomationMode.BASIC]
            },
            "pro": {
                "max_concurrent_sessions": 3,
                "enable_form_interaction": True,
                "enable_file_downloads": True,
                "enable_advanced_features": True,
                "max_blocked_domains": 20,
                "max_allowed_domains": 50,
                "session_timeout_minutes": 15,
                "available_modes": [
                    BrowserAutomationMode.DISABLED, 
                    BrowserAutomationMode.BASIC, 
                    BrowserAutomationMode.ADVANCED
                ]
            },
            "enterprise": {
                "max_concurrent_sessions": 10,
                "enable_form_interaction": True,
                "enable_file_downloads": True,
                "enable_advanced_features": True,
                "max_blocked_domains": 100,
                "max_allowed_domains": 200,
                "session_timeout_minutes": 30,
                "available_modes": [
                    BrowserAutomationMode.DISABLED,
                    BrowserAutomationMode.BASIC,
                    BrowserAutomationMode.ADVANCED,
                    BrowserAutomationMode.FULL
                ]
            }
        }
    
    def _initialize_config_templates(self):
        """Initialize configuration templates for different use cases"""
        
        # Basic template for new users
        self.config_templates["basic"] = BrowserAutomationConfig(
            enabled=True,
            mode=BrowserAutomationMode.BASIC,
            trigger=AutomationTrigger.INTENT_BASED,
            enable_navigation=True,
            enable_data_extraction=True,
            enable_form_interaction=False,
            enable_verification=True,
            max_concurrent_sessions=1,
            session_timeout_minutes=10
        )
        
        # Research template for data gathering
        self.config_templates["research"] = BrowserAutomationConfig(
            enabled=True,
            mode=BrowserAutomationMode.ADVANCED,
            trigger=AutomationTrigger.KEYWORD_BASED,
            enable_navigation=True,
            enable_data_extraction=True,
            enable_screenshot_capture=True,
            enable_verification=True,
            enable_role_routing=True,
            preferred_roles=["research", "analysis"],
            max_concurrent_sessions=2,
            session_timeout_minutes=15
        )
        
        # Business template for professional use
        self.config_templates["business"] = BrowserAutomationConfig(
            enabled=True,
            mode=BrowserAutomationMode.FULL,
            trigger=AutomationTrigger.INTENT_BASED,
            enable_navigation=True,
            enable_data_extraction=True,
            enable_form_interaction=True,
            enable_file_downloads=True,
            enable_screenshot_capture=True,
            enable_verification=True,
            enable_role_routing=True,
            enable_performance_optimization=True,
            max_concurrent_sessions=5,
            session_timeout_minutes=20,
            notify_on_completion=True
        )
    
    async def get_user_config(self, user_id: str, config_name: str = "default") -> Optional[UserCustomConfig]:
        """
        Get user's custom configuration including browser automation settings
        
        Args:
            user_id: User identifier
            config_name: Configuration name (default: "default")
            
        Returns:
            User configuration or None if not found
        """
        try:
            config_key = f"{user_id}_{config_name}"
            
            if config_key in self.user_configs:
                config = self.user_configs[config_key]
                config.last_used = datetime.now()
                return config
            
            # Create default configuration if not exists
            return await self._create_default_config(user_id, config_name)
            
        except Exception as e:
            self.log_error(f"Failed to get user config: {e}")
            return None
    
    async def update_user_config(
        self,
        user_id: str,
        config_updates: Dict[str, Any],
        config_name: str = "default"
    ) -> bool:
        """
        Update user's configuration with validation
        
        Args:
            user_id: User identifier
            config_updates: Configuration updates to apply
            config_name: Configuration name
            
        Returns:
            Success status
        """
        try:
            config_key = f"{user_id}_{config_name}"
            
            # Get existing config or create new one
            config = await self.get_user_config(user_id, config_name)
            if not config:
                return False
            
            # Validate updates against tier restrictions
            if not await self._validate_config_updates(user_id, config_updates):
                self.log_warning(f"Config updates validation failed for user: {user_id}")
                return False
            
            # Apply updates
            await self._apply_config_updates(config, config_updates)
            
            # Update timestamp
            config.updated_at = datetime.now()
            
            # Save configuration
            self.user_configs[config_key] = config
            
            self.log_info(f"User config updated: {user_id}", config_name=config_name)
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to update user config: {e}")
            return False
    
    async def toggle_browser_automation(
        self,
        user_id: str,
        enabled: bool,
        config_name: str = "default"
    ) -> Dict[str, Any]:
        """
        Toggle browser automation for user with validation
        
        Args:
            user_id: User identifier
            enabled: Enable/disable browser automation
            config_name: Configuration name
            
        Returns:
            Toggle result with status and details
        """
        try:
            # Check user access
            access_result = await access_control_manager.check_access(
                user_id=user_id,
                feature="browser_automation"
            )
            
            if not access_result.get("access_granted", False):
                return {
                    "success": False,
                    "error": "Browser automation not available for your subscription tier",
                    "required_tier": access_result.get("required_tier", "pro")
                }
            
            # Get user config
            config = await self.get_user_config(user_id, config_name)
            if not config:
                return {"success": False, "error": "User configuration not found"}
            
            # Update browser automation settings
            config.enable_browser_automation = enabled
            config.browser_automation.enabled = enabled
            
            if enabled:
                # Set appropriate mode based on tier
                user_tier = config.subscription_tier
                available_modes = self.tier_restrictions.get(user_tier, {}).get("available_modes", [])
                
                if BrowserAutomationMode.BASIC in available_modes:
                    config.browser_automation.mode = BrowserAutomationMode.BASIC
                else:
                    config.browser_automation.mode = BrowserAutomationMode.DISABLED
                    enabled = False
                    config.enable_browser_automation = False
            else:
                config.browser_automation.mode = BrowserAutomationMode.DISABLED
            
            # Update timestamp
            config.updated_at = datetime.now()
            
            # Save configuration
            config_key = f"{user_id}_{config_name}"
            self.user_configs[config_key] = config
            
            self.log_info(f"Browser automation toggled: {user_id}", enabled=enabled)
            
            return {
                "success": True,
                "enabled": enabled,
                "mode": config.browser_automation.mode.value,
                "available_features": await self._get_available_features(user_id),
                "usage_info": await self._get_usage_info(user_id)
            }
            
        except Exception as e:
            self.log_error(f"Failed to toggle browser automation: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_config_ui_data(self, user_id: str) -> Dict[str, Any]:
        """
        Get configuration UI data for custom model setup page
        
        Args:
            user_id: User identifier
            
        Returns:
            UI configuration data
        """
        try:
            # Get user's subscription tier
            user_limits = await access_control_manager.get_user_limits(user_id)
            subscription_tier = user_limits.get("subscription_tier", "starter")
            
            # Get tier restrictions
            tier_restrictions = self.tier_restrictions.get(subscription_tier, {})
            
            # Get current configuration
            current_config = await self.get_user_config(user_id)
            
            # Get available templates
            available_templates = await self._get_available_templates(subscription_tier)
            
            # Get usage statistics
            usage_stats = await self._get_usage_statistics(user_id)
            
            return {
                "browser_automation": {
                    "available": subscription_tier in ["pro", "enterprise"],
                    "current_status": {
                        "enabled": current_config.enable_browser_automation if current_config else False,
                        "mode": current_config.browser_automation.mode.value if current_config else "disabled",
                        "last_used": current_config.last_used.isoformat() if current_config and current_config.last_used else None
                    },
                    "tier_info": {
                        "current_tier": subscription_tier,
                        "required_tier": "pro" if subscription_tier == "starter" else subscription_tier,
                        "upgrade_required": subscription_tier == "starter"
                    },
                    "features": {
                        "available_modes": [mode.value for mode in tier_restrictions.get("available_modes", [])],
                        "max_concurrent_sessions": tier_restrictions.get("max_concurrent_sessions", 1),
                        "session_timeout_minutes": tier_restrictions.get("session_timeout_minutes", 5),
                        "advanced_features": tier_restrictions.get("enable_advanced_features", False),
                        "form_interaction": tier_restrictions.get("enable_form_interaction", False),
                        "file_downloads": tier_restrictions.get("enable_file_downloads", False)
                    },
                    "templates": available_templates,
                    "usage": usage_stats
                },
                "integration_settings": {
                    "enable_with_chat": True,
                    "priority_level": current_config.browser_automation_priority if current_config else 5,
                    "trigger_options": [trigger.value for trigger in AutomationTrigger],
                    "role_routing": {
                        "enabled": current_config.browser_automation.enable_role_routing if current_config else True,
                        "preferred_roles": current_config.browser_automation.preferred_roles if current_config else []
                    }
                }
            }
            
        except Exception as e:
            self.log_error(f"Failed to get config UI data: {e}")
            return {"error": str(e)}
    
    async def apply_template(
        self,
        user_id: str,
        template_name: str,
        config_name: str = "default"
    ) -> Dict[str, Any]:
        """
        Apply a configuration template to user's settings
        
        Args:
            user_id: User identifier
            template_name: Template to apply
            config_name: Configuration name
            
        Returns:
            Application result
        """
        try:
            if template_name not in self.config_templates:
                return {"success": False, "error": f"Template not found: {template_name}"}
            
            # Get template
            template = self.config_templates[template_name]
            
            # Validate template against user's tier
            user_limits = await access_control_manager.get_user_limits(user_id)
            subscription_tier = user_limits.get("subscription_tier", "starter")
            
            if not await self._validate_template_for_tier(template, subscription_tier):
                return {
                    "success": False,
                    "error": f"Template '{template_name}' not available for {subscription_tier} tier"
                }
            
            # Get or create user config
            config = await self.get_user_config(user_id, config_name)
            if not config:
                return {"success": False, "error": "Failed to get user configuration"}
            
            # Apply template with tier restrictions
            config.browser_automation = await self._apply_tier_restrictions(template, subscription_tier)
            config.enable_browser_automation = True
            config.updated_at = datetime.now()
            
            # Save configuration
            config_key = f"{user_id}_{config_name}"
            self.user_configs[config_key] = config
            
            self.log_info(f"Template applied: {user_id}", template=template_name)
            
            return {
                "success": True,
                "template_applied": template_name,
                "configuration": {
                    "mode": config.browser_automation.mode.value,
                    "features_enabled": await self._get_enabled_features(config.browser_automation),
                    "restrictions_applied": await self._get_applied_restrictions(subscription_tier)
                }
            }
            
        except Exception as e:
            self.log_error(f"Failed to apply template: {e}")
            return {"success": False, "error": str(e)}

    # Helper methods
    async def _create_default_config(self, user_id: str, config_name: str) -> UserCustomConfig:
        """Create default configuration for user"""
        try:
            # Get user's subscription tier
            user_limits = await access_control_manager.get_user_limits(user_id)
            subscription_tier = user_limits.get("subscription_tier", "starter")

            # Create default browser automation config based on tier
            browser_config = BrowserAutomationConfig()

            # Apply tier restrictions
            tier_restrictions = self.tier_restrictions.get(subscription_tier, {})
            browser_config.max_concurrent_sessions = tier_restrictions.get("max_concurrent_sessions", 1)
            browser_config.session_timeout_minutes = tier_restrictions.get("session_timeout_minutes", 5)
            browser_config.enable_form_interaction = tier_restrictions.get("enable_form_interaction", False)
            browser_config.enable_file_downloads = tier_restrictions.get("enable_file_downloads", False)

            # Create user config
            config = UserCustomConfig(
                user_id=user_id,
                config_name=config_name,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                browser_automation=browser_config,
                subscription_tier=subscription_tier
            )

            # Save configuration
            config_key = f"{user_id}_{config_name}"
            self.user_configs[config_key] = config

            self.log_info(f"Default config created: {user_id}", config_name=config_name)

            return config

        except Exception as e:
            self.log_error(f"Failed to create default config: {e}")
            raise

    async def _validate_config_updates(self, user_id: str, config_updates: Dict[str, Any]) -> bool:
        """Validate configuration updates against tier restrictions"""
        try:
            # Get user's subscription tier
            user_limits = await access_control_manager.get_user_limits(user_id)
            subscription_tier = user_limits.get("subscription_tier", "starter")
            tier_restrictions = self.tier_restrictions.get(subscription_tier, {})

            # Validate browser automation updates
            if "browser_automation" in config_updates:
                browser_updates = config_updates["browser_automation"]

                # Check mode availability
                if "mode" in browser_updates:
                    requested_mode = BrowserAutomationMode(browser_updates["mode"])
                    available_modes = tier_restrictions.get("available_modes", [])
                    if requested_mode not in available_modes:
                        return False

                # Check concurrent sessions limit
                if "max_concurrent_sessions" in browser_updates:
                    max_allowed = tier_restrictions.get("max_concurrent_sessions", 1)
                    if browser_updates["max_concurrent_sessions"] > max_allowed:
                        return False

                # Check advanced features
                if not tier_restrictions.get("enable_advanced_features", False):
                    restricted_features = [
                        "enable_form_interaction",
                        "enable_file_downloads",
                        "custom_user_agent",
                        "custom_viewport_size"
                    ]
                    for feature in restricted_features:
                        if browser_updates.get(feature, False):
                            return False

            return True

        except Exception as e:
            self.log_error(f"Config validation failed: {e}")
            return False

    async def _apply_config_updates(self, config: UserCustomConfig, updates: Dict[str, Any]):
        """Apply configuration updates to user config"""
        try:
            # Update browser automation settings
            if "browser_automation" in updates:
                browser_updates = updates["browser_automation"]

                for key, value in browser_updates.items():
                    if hasattr(config.browser_automation, key):
                        if key == "mode":
                            config.browser_automation.mode = BrowserAutomationMode(value)
                        elif key == "trigger":
                            config.browser_automation.trigger = AutomationTrigger(value)
                        else:
                            setattr(config.browser_automation, key, value)

            # Update general settings
            if "enable_browser_automation" in updates:
                config.enable_browser_automation = updates["enable_browser_automation"]

            if "browser_automation_priority" in updates:
                config.browser_automation_priority = updates["browser_automation_priority"]

            # Update model configurations (existing RouKey functionality)
            if "model_configs" in updates:
                config.model_configs.update(updates["model_configs"])

            if "routing_strategies" in updates:
                config.routing_strategies.update(updates["routing_strategies"])

            if "role_assignments" in updates:
                config.role_assignments.update(updates["role_assignments"])

        except Exception as e:
            self.log_error(f"Failed to apply config updates: {e}")
            raise

    async def _get_available_features(self, user_id: str) -> Dict[str, bool]:
        """Get available features for user based on tier"""
        try:
            user_limits = await access_control_manager.get_user_limits(user_id)
            subscription_tier = user_limits.get("subscription_tier", "starter")
            tier_restrictions = self.tier_restrictions.get(subscription_tier, {})

            return {
                "basic_navigation": True,
                "data_extraction": True,
                "form_interaction": tier_restrictions.get("enable_form_interaction", False),
                "file_downloads": tier_restrictions.get("enable_file_downloads", False),
                "screenshot_capture": True,
                "verification": True,
                "role_routing": True,
                "performance_optimization": subscription_tier in ["pro", "enterprise"],
                "advanced_settings": tier_restrictions.get("enable_advanced_features", False),
                "multiple_sessions": tier_restrictions.get("max_concurrent_sessions", 1) > 1
            }

        except Exception as e:
            self.log_error(f"Failed to get available features: {e}")
            return {}

    async def _get_usage_info(self, user_id: str) -> Dict[str, Any]:
        """Get usage information for user"""
        try:
            # Get current usage from access control manager
            quota_status = await access_control_manager.check_browsing_quota(user_id)

            return {
                "current_usage": quota_status.get("current_usage", 0),
                "monthly_limit": quota_status.get("monthly_limit", 0),
                "remaining": quota_status.get("remaining", 0),
                "reset_date": quota_status.get("reset_date", ""),
                "can_browse": quota_status.get("can_browse", False)
            }

        except Exception as e:
            self.log_error(f"Failed to get usage info: {e}")
            return {}

    async def _get_available_templates(self, subscription_tier: str) -> Dict[str, Any]:
        """Get available configuration templates for tier"""
        try:
            available_templates = {}

            for template_name, template_config in self.config_templates.items():
                if await self._validate_template_for_tier(template_config, subscription_tier):
                    available_templates[template_name] = {
                        "name": template_name.title(),
                        "description": self._get_template_description(template_name),
                        "mode": template_config.mode.value,
                        "features": await self._get_template_features(template_config),
                        "recommended_for": self._get_template_recommendations(template_name)
                    }

            return available_templates

        except Exception as e:
            self.log_error(f"Failed to get available templates: {e}")
            return {}

    async def _get_usage_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get usage statistics for user"""
        try:
            # This would integrate with actual usage tracking
            return {
                "total_sessions": 0,
                "successful_tasks": 0,
                "failed_tasks": 0,
                "average_session_duration": 0,
                "most_used_features": [],
                "last_activity": None
            }

        except Exception as e:
            self.log_error(f"Failed to get usage statistics: {e}")
            return {}

    async def _validate_template_for_tier(self, template: BrowserAutomationConfig, tier: str) -> bool:
        """Validate if template is available for subscription tier"""
        try:
            tier_restrictions = self.tier_restrictions.get(tier, {})

            # Check mode availability
            available_modes = tier_restrictions.get("available_modes", [])
            if template.mode not in available_modes:
                return False

            # Check advanced features
            if not tier_restrictions.get("enable_advanced_features", False):
                if (template.enable_form_interaction or
                    template.enable_file_downloads or
                    template.custom_user_agent or
                    template.custom_viewport_size):
                    return False

            # Check session limits
            max_sessions = tier_restrictions.get("max_concurrent_sessions", 1)
            if template.max_concurrent_sessions > max_sessions:
                return False

            return True

        except Exception as e:
            self.log_error(f"Template validation failed: {e}")
            return False

    async def _apply_tier_restrictions(self, template: BrowserAutomationConfig, tier: str) -> BrowserAutomationConfig:
        """Apply tier restrictions to template configuration"""
        try:
            # Create a copy of the template
            restricted_config = BrowserAutomationConfig(
                enabled=template.enabled,
                mode=template.mode,
                trigger=template.trigger,
                enable_navigation=template.enable_navigation,
                enable_data_extraction=template.enable_data_extraction,
                enable_screenshot_capture=template.enable_screenshot_capture,
                enable_verification=template.enable_verification,
                enable_role_routing=template.enable_role_routing,
                preferred_roles=template.preferred_roles.copy(),
                enable_fallback_strategies=template.enable_fallback_strategies,
                enable_error_recovery=template.enable_error_recovery
            )

            # Apply tier restrictions
            tier_restrictions = self.tier_restrictions.get(tier, {})

            # Restrict concurrent sessions
            restricted_config.max_concurrent_sessions = min(
                template.max_concurrent_sessions,
                tier_restrictions.get("max_concurrent_sessions", 1)
            )

            # Restrict session timeout
            restricted_config.session_timeout_minutes = min(
                template.session_timeout_minutes,
                tier_restrictions.get("session_timeout_minutes", 5)
            )

            # Restrict advanced features
            if not tier_restrictions.get("enable_advanced_features", False):
                restricted_config.enable_form_interaction = False
                restricted_config.enable_file_downloads = False
                restricted_config.custom_user_agent = None
                restricted_config.custom_viewport_size = None
            else:
                restricted_config.enable_form_interaction = template.enable_form_interaction
                restricted_config.enable_file_downloads = template.enable_file_downloads
                restricted_config.custom_user_agent = template.custom_user_agent
                restricted_config.custom_viewport_size = template.custom_viewport_size

            return restricted_config

        except Exception as e:
            self.log_error(f"Failed to apply tier restrictions: {e}")
            return template

    def _get_template_description(self, template_name: str) -> str:
        """Get description for template"""
        descriptions = {
            "basic": "Simple navigation and data extraction for everyday tasks",
            "research": "Advanced data gathering with verification and role routing",
            "business": "Full-featured automation for professional workflows"
        }
        return descriptions.get(template_name, "Custom configuration template")

    async def _get_template_features(self, template: BrowserAutomationConfig) -> List[str]:
        """Get list of features enabled in template"""
        features = []

        if template.enable_navigation:
            features.append("Web Navigation")
        if template.enable_data_extraction:
            features.append("Data Extraction")
        if template.enable_form_interaction:
            features.append("Form Interaction")
        if template.enable_file_downloads:
            features.append("File Downloads")
        if template.enable_screenshot_capture:
            features.append("Screenshot Capture")
        if template.enable_verification:
            features.append("Result Verification")
        if template.enable_role_routing:
            features.append("Role-based Routing")
        if template.enable_performance_optimization:
            features.append("Performance Optimization")

        return features

    def _get_template_recommendations(self, template_name: str) -> List[str]:
        """Get recommendations for template usage"""
        recommendations = {
            "basic": [
                "New users getting started with browser automation",
                "Simple data extraction tasks",
                "Basic web navigation needs"
            ],
            "research": [
                "Academic research and data gathering",
                "Market research and analysis",
                "Content verification workflows"
            ],
            "business": [
                "Professional automation workflows",
                "Complex data processing tasks",
                "Enterprise-level integrations"
            ]
        }
        return recommendations.get(template_name, [])

    async def _get_enabled_features(self, config: BrowserAutomationConfig) -> List[str]:
        """Get list of enabled features in configuration"""
        return await self._get_template_features(config)

    async def _get_applied_restrictions(self, tier: str) -> Dict[str, Any]:
        """Get applied restrictions for tier"""
        return self.tier_restrictions.get(tier, {})


# Global user configuration manager instance
user_config_manager = UserConfigManager()
