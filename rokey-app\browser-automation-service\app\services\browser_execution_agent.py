"""
Browser Execution Agent
Advanced browser automation agent with full Browser Use capabilities and memory integration
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncGenerator
from browser_use import Agent as BrowserUseAgent, Controller, ActionResult
from browser_use.agent.memory import MemoryConfig
from playwright.async_api import Page, BrowserContext
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from app.models.browser_automation import TodoItem, TaskStatus, BrowserSession
from app.services.browser_pool import BrowserPool
from app.services.session_manager import SessionManager
from app.services.browser_functions import BrowserFunctions
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserUseException, BrowserAutomationException


class BrowserExecutionAgent(LoggerMixin):
    """
    Advanced browser execution agent with full Browser Use integration
    
    Features:
    - Full Browser Use capabilities (scrolling, clicking, forms, calendar, extraction)
    - Memory functionality with Mem0 integration
    - Session persistence and context management
    - Custom RouKey-specific functions
    - Error handling and recovery
    - Progress tracking and reporting
    """
    
    def __init__(
        self,
        role: str,
        llm_config: Dict[str, Any],
        session_manager: SessionManager,
        browser_pool: BrowserPool,
        enable_memory: bool = True,
        memory_config: Optional[MemoryConfig] = None,
        system_prompt: Optional[str] = None
    ):
        self.role = role
        self.llm_config = llm_config
        self.session_manager = session_manager
        self.browser_pool = browser_pool
        self.enable_memory = enable_memory
        self.memory_config = memory_config
        self.system_prompt = system_prompt or self._get_default_system_prompt()
        
        # Browser Use components
        self.browser_agent: Optional[BrowserUseAgent] = None
        self.controller: Optional[Controller] = None
        self.browser_functions = BrowserFunctions()
        
        # Session state
        self.current_session: Optional[BrowserSession] = None
        self.current_context: Optional[BrowserContext] = None
        self.current_page: Optional[Page] = None
        
        # Execution state
        self.current_todo_list: List[TodoItem] = []
        self.execution_metadata: Dict[str, Any] = {}
        
        self.log_info(f"Browser execution agent initialized for role: {role}")
    
    async def initialize(self, session_id: str, task_description: str) -> None:
        """Initialize the browser execution agent"""
        try:
            self.log_info("Initializing browser execution agent", session_id=session_id)
            
            # Get or create browser session
            self.current_session = await self.session_manager.get_session(session_id)
            if not self.current_session:
                self.current_session = await self.session_manager.create_session(
                    user_id="browser_agent",
                    task_id=session_id
                )
            
            # Get browser context from pool
            browser_instance = await self.browser_pool.get_browser_instance(session_id)
            self.current_context = await browser_instance.create_context(
                session_id,
                viewport={
                    'width': 1920,
                    'height': 1080
                },
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # Create new page
            self.current_page = await self.current_context.new_page()
            
            # Create controller with custom functions
            self.controller = Controller()
            self.browser_functions.register_functions(self.controller)
            
            # Create Browser Use agent
            self.browser_agent = await self._create_browser_use_agent(task_description)
            
            # Initialize execution metadata
            self.execution_metadata = {
                "session_id": session_id,
                "start_time": datetime.now(),
                "steps_completed": 0,
                "actions_performed": [],
                "errors_encountered": [],
                "pages_visited": [],
                "data_extracted": {},
                "screenshots_taken": []
            }
            
            self.log_info("Browser execution agent initialized successfully")
            
        except Exception as e:
            self.log_error(f"Failed to initialize browser execution agent: {e}")
            raise BrowserUseException(f"Agent initialization failed: {e}")
    
    async def execute_task(
        self,
        task: str,
        max_steps: int = 20,
        enable_screenshots: bool = True
    ) -> Dict[str, Any]:
        """Execute a browser automation task"""
        try:
            self.log_info(f"Executing browser task: {task[:100]}")
            
            # Update agent task
            if self.browser_agent:
                self.browser_agent.task = task
            
            # Execute with Browser Use
            result = await self.browser_agent.run(
                max_steps=max_steps,
                page=self.current_page
            )
            
            # Process and format results
            execution_result = await self._process_execution_result(
                result, 
                task, 
                enable_screenshots
            )
            
            self.log_info("Browser task execution completed", 
                         steps=execution_result.get("steps_completed", 0))
            
            return execution_result
            
        except Exception as e:
            self.log_error(f"Browser task execution failed: {e}")
            await self._handle_execution_error(e, task)
            raise BrowserUseException(f"Task execution failed: {e}")
    
    async def execute_todo_item(
        self,
        todo_item: TodoItem,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific todo item"""
        try:
            self.log_info(f"Executing todo item: {todo_item.task}")
            
            # Update todo item status
            todo_item.status = TaskStatus.IN_PROGRESS
            await self._update_todo_progress(todo_item)
            
            # Execute the task
            result = await self.execute_task(
                task=todo_item.task,
                max_steps=10,  # Limit steps per todo item
                enable_screenshots=True
            )
            
            # Update todo item with results
            todo_item.status = TaskStatus.COMPLETED
            todo_item.completion_time = datetime.now()
            todo_item.result = result
            await self._update_todo_progress(todo_item)
            
            self.log_info(f"Todo item completed: {todo_item.id}")
            
            return {
                "todo_id": todo_item.id,
                "status": "completed",
                "result": result,
                "completion_time": todo_item.completion_time.isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Todo item execution failed: {e}")
            todo_item.status = TaskStatus.FAILED
            todo_item.error_message = str(e)
            await self._update_todo_progress(todo_item)
            
            return {
                "todo_id": todo_item.id,
                "status": "failed",
                "error": str(e)
            }
    
    async def execute_with_streaming(
        self,
        task: str,
        max_steps: int = 20
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute task with streaming progress updates"""
        try:
            self.log_info(f"Starting streaming execution: {task[:100]}")
            
            # Yield initial status
            yield {
                "type": "status",
                "message": "Starting browser automation",
                "progress": 0,
                "timestamp": datetime.now().isoformat()
            }
            
            # Update agent task
            if self.browser_agent:
                self.browser_agent.task = task
            
            # Execute with streaming (simulated for now)
            step_count = 0
            for step in range(max_steps):
                try:
                    # Simulate step execution
                    await asyncio.sleep(1)  # Simulate work
                    step_count += 1
                    
                    # Yield progress update
                    yield {
                        "type": "progress",
                        "step": step_count,
                        "max_steps": max_steps,
                        "progress": (step_count / max_steps) * 100,
                        "current_action": f"Executing step {step_count}",
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Break if task is complete (simplified logic)
                    if step_count >= 5:  # Simulate completion
                        break
                        
                except Exception as e:
                    yield {
                        "type": "error",
                        "message": str(e),
                        "step": step_count,
                        "timestamp": datetime.now().isoformat()
                    }
                    break
            
            # Yield final result
            yield {
                "type": "completion",
                "message": "Browser automation completed",
                "steps_completed": step_count,
                "result": "Task completed successfully",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Streaming execution failed: {e}")
            yield {
                "type": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def extract_data(
        self,
        extraction_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract structured data from current page"""
        try:
            if not self.current_page:
                raise BrowserUseException("No active page for data extraction")
            
            extraction_type = extraction_config.get("type", "general")
            selectors = extraction_config.get("selectors", [])
            
            self.log_info(f"Extracting data: {extraction_type}")
            
            # Use custom browser function for extraction
            result = await self.browser_functions.extract_structured_data(
                data_type=extraction_type,
                css_selectors=selectors,
                page=self.current_page
            )
            
            # Store extracted data
            self.execution_metadata["data_extracted"][extraction_type] = result
            
            return {
                "extraction_type": extraction_type,
                "data": result,
                "timestamp": datetime.now().isoformat(),
                "source_url": self.current_page.url
            }
            
        except Exception as e:
            self.log_error(f"Data extraction failed: {e}")
            raise BrowserUseException(f"Data extraction failed: {e}")
    
    async def take_screenshot(self, name: str = None) -> str:
        """Take a screenshot of the current page"""
        try:
            if not self.current_page:
                raise BrowserUseException("No active page for screenshot")
            
            screenshot_name = name or f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            screenshot_path = f"screenshots/{screenshot_name}"
            
            # Take screenshot
            await self.current_page.screenshot(path=screenshot_path, full_page=True)
            
            # Store screenshot info
            self.execution_metadata["screenshots_taken"].append({
                "name": screenshot_name,
                "path": screenshot_path,
                "timestamp": datetime.now().isoformat(),
                "url": self.current_page.url
            })
            
            self.log_info(f"Screenshot taken: {screenshot_name}")
            
            return screenshot_path
            
        except Exception as e:
            self.log_error(f"Screenshot failed: {e}")
            raise BrowserUseException(f"Screenshot failed: {e}")
    
    async def navigate_to_url(self, url: str, wait_for: str = "load") -> Dict[str, Any]:
        """Navigate to a specific URL"""
        try:
            if not self.current_page:
                raise BrowserUseException("No active page for navigation")
            
            self.log_info(f"Navigating to: {url}")
            
            # Navigate to URL
            response = await self.current_page.goto(url, wait_until=wait_for)
            
            # Update session state
            if self.current_session:
                self.current_session.current_url = url
                await self.session_manager.update_session(self.current_session)
            
            # Record navigation
            self.execution_metadata["pages_visited"].append({
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "status_code": response.status if response else None
            })
            
            return {
                "url": url,
                "status": "success",
                "page_title": await self.current_page.title(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Navigation failed: {e}")
            raise BrowserUseException(f"Navigation to {url} failed: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup browser resources"""
        try:
            self.log_info("Cleaning up browser execution agent")
            
            # Close page
            if self.current_page:
                await self.current_page.close()
            
            # Close context
            if self.current_context:
                await self.current_context.close()
            
            # Update session
            if self.current_session:
                await self.session_manager.update_session(self.current_session)
            
            # Clear references
            self.current_page = None
            self.current_context = None
            self.browser_agent = None
            
            self.log_info("Browser execution agent cleanup completed")
            
        except Exception as e:
            self.log_error(f"Cleanup failed: {e}")
    
    async def _create_browser_use_agent(self, task_description: str) -> BrowserUseAgent:
        """Create Browser Use agent with full configuration"""
        from langchain_openai import ChatOpenAI
        from langchain_anthropic import ChatAnthropic
        
        # Create LLM instance
        provider = self.llm_config.get("provider", "openai").lower()
        api_key = self.llm_config.get("api_key")
        model = self.llm_config.get("model", "gpt-4o")
        
        if provider == "openai":
            llm = ChatOpenAI(
                api_key=api_key,
                model=model,
                temperature=self.llm_config.get("temperature", 0.7),
                max_tokens=self.llm_config.get("max_tokens", 2000)
            )
        elif provider == "anthropic":
            llm = ChatAnthropic(
                api_key=api_key,
                model=model,
                temperature=self.llm_config.get("temperature", 0.7),
                max_tokens=self.llm_config.get("max_tokens", 2000)
            )
        else:
            # Fallback to OpenAI
            from app.core.config import settings
            llm = ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                model="gpt-4o",
                temperature=0.7,
                max_tokens=2000
            )
        
        # Create Browser Use agent
        agent = BrowserUseAgent(
            task=task_description,
            llm=llm,
            controller=self.controller,
            use_vision=True,
            enable_memory=self.enable_memory,
            memory_config=self.memory_config,
            override_system_message=self.system_prompt
        )
        
        return agent
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt for browser execution"""
        return f"""You are an expert browser automation agent for RouKey, specializing in the {self.role} role.

Your capabilities include:
- Navigating websites and web applications
- Clicking buttons, links, and interactive elements
- Filling out forms and input fields
- Scrolling and interacting with page content
- Extracting information and data from web pages
- Taking screenshots for documentation
- Handling calendars, date pickers, and complex UI elements
- Managing multiple tabs and windows

Key principles:
1. Be precise and efficient in your actions
2. Always verify actions were successful before proceeding
3. Extract relevant information accurately
4. Handle errors gracefully and retry when appropriate
5. Maintain context and memory across actions
6. Report progress clearly and regularly

For the {self.role} role, focus on tasks that align with this specialization while maintaining general browser automation capabilities.

Always prioritize user safety and respect website terms of service."""
    
    async def _process_execution_result(
        self,
        result: Any,
        task: str,
        enable_screenshots: bool
    ) -> Dict[str, Any]:
        """Process and format execution results"""
        
        # Take final screenshot if enabled
        screenshot_path = None
        if enable_screenshots and self.current_page:
            try:
                screenshot_path = await self.take_screenshot("final_result")
            except Exception as e:
                self.log_warning(f"Failed to take final screenshot: {e}")
        
        # Update execution metadata
        self.execution_metadata["end_time"] = datetime.now()
        self.execution_metadata["steps_completed"] += 1
        
        return {
            "task": task,
            "result": str(result),
            "success": True,
            "execution_metadata": self.execution_metadata,
            "final_screenshot": screenshot_path,
            "current_url": self.current_page.url if self.current_page else None,
            "page_title": await self.current_page.title() if self.current_page else None,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _handle_execution_error(self, error: Exception, task: str):
        """Handle execution errors"""
        error_info = {
            "error": str(error),
            "error_type": type(error).__name__,
            "task": task,
            "timestamp": datetime.now().isoformat(),
            "current_url": self.current_page.url if self.current_page else None
        }
        
        self.execution_metadata["errors_encountered"].append(error_info)
        
        # Try to take error screenshot
        try:
            if self.current_page:
                await self.take_screenshot("error_state")
        except Exception:
            pass  # Ignore screenshot errors during error handling
    
    async def _update_todo_progress(self, todo_item: TodoItem):
        """Update todo item progress"""
        try:
            # Use custom browser function to update progress
            if self.browser_functions:
                await self.browser_functions.update_todo_progress(
                    task_id=todo_item.id,
                    status=todo_item.status.value,
                    result=str(todo_item.result) if todo_item.result else None,
                    page=self.current_page
                )
        except Exception as e:
            self.log_warning(f"Failed to update todo progress: {e}")
