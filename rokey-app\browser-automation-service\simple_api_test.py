#!/usr/bin/env python3
"""
Simple API Test Script
Tests if the browser automation API is working
"""

import asyncio
import httpx
import time

async def test_api():
    """Test the API endpoints"""
    
    print("🚀 Testing Browser Automation API...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test health endpoint
            print("1️⃣ Testing Health Endpoint...")
            response = await client.get("http://127.0.0.1:8000/health")
            
            if response.status_code == 200:
                print("   ✅ Health endpoint working!")
                print(f"   📊 Response: {response.json()}")
            else:
                print(f"   ❌ Health endpoint failed: {response.status_code}")
                return False
            
            # Test service info
            print("\n2️⃣ Testing Service Info...")
            response = await client.get("http://127.0.0.1:8000/info")
            
            if response.status_code == 200:
                print("   ✅ Service info working!")
                info = response.json()
                print(f"   📊 Service: {info.get('service', 'unknown')}")
                print(f"   📦 Version: {info.get('version', 'unknown')}")
            else:
                print(f"   ❌ Service info failed: {response.status_code}")
            
            # Test API documentation
            print("\n3️⃣ Testing API Documentation...")
            response = await client.get("http://127.0.0.1:8000/docs")
            
            if response.status_code == 200:
                print("   ✅ API documentation accessible!")
            else:
                print(f"   ❌ API documentation failed: {response.status_code}")
            
            print("\n🎉 API is working correctly!")
            return True
            
    except httpx.ConnectError:
        print("❌ Cannot connect to API server. Is it running on port 8000?")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

if __name__ == "__main__":
    print("📋 Simple API Test")
    print("=" * 40)
    
    success = asyncio.run(test_api())
    
    if success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
