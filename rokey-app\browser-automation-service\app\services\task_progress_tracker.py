"""
Task Progress Tracker
Real-time progress monitoring and analytics for browser automation workflows
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import json

from app.models.browser_automation import TaskStatus
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class ProgressEventType(Enum):
    """Types of progress events"""
    TASK_STARTED = "task_started"
    TASK_PROGRESS = "task_progress"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_PROGRESS = "workflow_progress"
    WORKFLOW_COMPLETED = "workflow_completed"
    MILESTONE_REACHED = "milestone_reached"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class ProgressEvent:
    """Progress event data structure"""
    event_type: ProgressEventType
    timestamp: datetime
    workflow_id: str
    task_id: Optional[str] = None
    progress_percentage: float = 0.0
    message: str = ""
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "workflow_id": self.workflow_id,
            "task_id": self.task_id,
            "progress_percentage": self.progress_percentage,
            "message": self.message,
            "details": self.details
        }


@dataclass
class WorkflowProgress:
    """Comprehensive workflow progress information"""
    workflow_id: str
    start_time: datetime
    current_time: datetime
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    in_progress_tasks: int
    pending_tasks: int
    progress_percentage: float
    estimated_completion: Optional[datetime]
    current_phase: str
    milestones: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "workflow_id": self.workflow_id,
            "start_time": self.start_time.isoformat(),
            "current_time": self.current_time.isoformat(),
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "in_progress_tasks": self.in_progress_tasks,
            "pending_tasks": self.pending_tasks,
            "progress_percentage": self.progress_percentage,
            "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
            "current_phase": self.current_phase,
            "milestones": self.milestones,
            "performance_metrics": self.performance_metrics
        }


class TaskProgressTracker(LoggerMixin):
    """
    Real-time task progress tracking and analytics system
    
    Features:
    - Real-time progress monitoring
    - Event-driven progress updates
    - Performance analytics and metrics
    - Milestone tracking and notifications
    - Progress estimation algorithms
    - Historical progress analysis
    - Custom progress callbacks
    - Progress visualization data
    """
    
    def __init__(self):
        # Progress tracking data
        self.workflow_progress: Dict[str, WorkflowProgress] = {}
        self.task_progress: Dict[str, Dict[str, Any]] = {}
        self.progress_events: List[ProgressEvent] = []
        
        # Event system
        self.progress_callbacks: Dict[str, List[Callable]] = {}
        self.milestone_definitions: Dict[str, List[Dict[str, Any]]] = {}
        
        # Performance tracking
        self.performance_history: Dict[str, List[Dict[str, Any]]] = {}
        self.progress_analytics: Dict[str, Any] = {}
        
        # Real-time monitoring
        self.active_monitors: Dict[str, asyncio.Task] = {}
        self.monitoring_interval = 1.0  # seconds
        
        self.log_info("Task progress tracker initialized")
    
    async def start_workflow_tracking(
        self,
        workflow_id: str,
        total_tasks: int,
        milestones: List[Dict[str, Any]] = None
    ) -> None:
        """
        Start tracking progress for a workflow
        
        Args:
            workflow_id: Unique identifier for the workflow
            total_tasks: Total number of tasks in the workflow
            milestones: List of milestone definitions
        """
        try:
            # Initialize workflow progress
            workflow_progress = WorkflowProgress(
                workflow_id=workflow_id,
                start_time=datetime.now(),
                current_time=datetime.now(),
                total_tasks=total_tasks,
                completed_tasks=0,
                failed_tasks=0,
                in_progress_tasks=0,
                pending_tasks=total_tasks,
                progress_percentage=0.0,
                estimated_completion=None,
                current_phase="initialization",
                milestones=milestones or [],
                performance_metrics={}
            )
            
            self.workflow_progress[workflow_id] = workflow_progress
            
            # Store milestone definitions
            if milestones:
                self.milestone_definitions[workflow_id] = milestones
            
            # Start real-time monitoring
            monitor_task = asyncio.create_task(
                self._monitor_workflow_progress(workflow_id)
            )
            self.active_monitors[workflow_id] = monitor_task
            
            # Emit workflow started event
            await self._emit_progress_event(ProgressEvent(
                event_type=ProgressEventType.WORKFLOW_STARTED,
                timestamp=datetime.now(),
                workflow_id=workflow_id,
                message=f"Workflow started with {total_tasks} tasks",
                details={"total_tasks": total_tasks, "milestones": len(milestones or [])}
            ))
            
            self.log_info(f"Started tracking workflow: {workflow_id}", total_tasks=total_tasks)
            
        except Exception as e:
            self.log_error(f"Failed to start workflow tracking: {e}")
            raise BrowserAutomationException(f"Workflow tracking initialization failed: {e}")
    
    async def update_task_progress(
        self,
        workflow_id: str,
        task_id: str,
        status: TaskStatus,
        progress_percentage: float = None,
        message: str = "",
        details: Dict[str, Any] = None
    ) -> None:
        """
        Update progress for a specific task
        
        Args:
            workflow_id: ID of the workflow containing the task
            task_id: ID of the task being updated
            status: Current status of the task
            progress_percentage: Progress percentage for the task (0-100)
            message: Progress message
            details: Additional progress details
        """
        try:
            if workflow_id not in self.workflow_progress:
                self.log_warning(f"Workflow not found for progress update: {workflow_id}")
                return
            
            # Update task progress
            if task_id not in self.task_progress:
                self.task_progress[task_id] = {}
            
            self.task_progress[task_id].update({
                "workflow_id": workflow_id,
                "status": status.value,
                "progress_percentage": progress_percentage or 0.0,
                "message": message,
                "details": details or {},
                "last_updated": datetime.now()
            })
            
            # Update workflow progress
            await self._update_workflow_progress(workflow_id)
            
            # Determine event type based on status
            if status == TaskStatus.IN_PROGRESS:
                event_type = ProgressEventType.TASK_STARTED if progress_percentage == 0 else ProgressEventType.TASK_PROGRESS
            elif status == TaskStatus.COMPLETED:
                event_type = ProgressEventType.TASK_COMPLETED
            elif status == TaskStatus.FAILED:
                event_type = ProgressEventType.TASK_FAILED
            else:
                event_type = ProgressEventType.TASK_PROGRESS
            
            # Emit task progress event
            await self._emit_progress_event(ProgressEvent(
                event_type=event_type,
                timestamp=datetime.now(),
                workflow_id=workflow_id,
                task_id=task_id,
                progress_percentage=progress_percentage or 0.0,
                message=message,
                details=details or {}
            ))
            
            # Check for milestones
            await self._check_milestones(workflow_id)
            
        except Exception as e:
            self.log_error(f"Failed to update task progress: {e}")
    
    async def get_workflow_progress(self, workflow_id: str) -> Optional[WorkflowProgress]:
        """
        Get current progress for a workflow
        
        Args:
            workflow_id: ID of the workflow
            
        Returns:
            WorkflowProgress object or None if not found
        """
        try:
            if workflow_id not in self.workflow_progress:
                return None
            
            # Update current time and recalculate metrics
            progress = self.workflow_progress[workflow_id]
            progress.current_time = datetime.now()
            
            # Update performance metrics
            progress.performance_metrics = await self._calculate_performance_metrics(workflow_id)
            
            return progress
            
        except Exception as e:
            self.log_error(f"Failed to get workflow progress: {e}")
            return None
    
    async def get_progress_events(
        self,
        workflow_id: str = None,
        event_types: List[ProgressEventType] = None,
        since: datetime = None,
        limit: int = 100
    ) -> List[ProgressEvent]:
        """
        Get progress events with optional filtering
        
        Args:
            workflow_id: Filter by workflow ID
            event_types: Filter by event types
            since: Filter events since this timestamp
            limit: Maximum number of events to return
            
        Returns:
            List of progress events
        """
        try:
            filtered_events = []
            
            for event in reversed(self.progress_events):  # Most recent first
                # Apply filters
                if workflow_id and event.workflow_id != workflow_id:
                    continue
                
                if event_types and event.event_type not in event_types:
                    continue
                
                if since and event.timestamp < since:
                    continue
                
                filtered_events.append(event)
                
                if len(filtered_events) >= limit:
                    break
            
            return filtered_events
            
        except Exception as e:
            self.log_error(f"Failed to get progress events: {e}")
            return []
    
    async def register_progress_callback(
        self,
        workflow_id: str,
        callback: Callable[[ProgressEvent], None],
        event_types: List[ProgressEventType] = None
    ) -> None:
        """
        Register a callback for progress events
        
        Args:
            workflow_id: Workflow ID to monitor
            callback: Function to call on progress events
            event_types: Specific event types to monitor (None for all)
        """
        try:
            callback_key = f"{workflow_id}:{id(callback)}"
            
            if callback_key not in self.progress_callbacks:
                self.progress_callbacks[callback_key] = []
            
            self.progress_callbacks[callback_key].append({
                "callback": callback,
                "event_types": event_types,
                "workflow_id": workflow_id
            })
            
            self.log_info(f"Progress callback registered for workflow: {workflow_id}")
            
        except Exception as e:
            self.log_error(f"Failed to register progress callback: {e}")
    
    async def set_workflow_milestones(
        self,
        workflow_id: str,
        milestones: List[Dict[str, Any]]
    ) -> None:
        """
        Set milestones for a workflow
        
        Args:
            workflow_id: ID of the workflow
            milestones: List of milestone definitions
                       Each milestone should have: name, percentage, description
        """
        try:
            self.milestone_definitions[workflow_id] = milestones
            
            if workflow_id in self.workflow_progress:
                self.workflow_progress[workflow_id].milestones = milestones
            
            self.log_info(f"Milestones set for workflow: {workflow_id}", count=len(milestones))
            
        except Exception as e:
            self.log_error(f"Failed to set workflow milestones: {e}")
    
    async def get_progress_analytics(self, workflow_id: str = None) -> Dict[str, Any]:
        """
        Get progress analytics and insights
        
        Args:
            workflow_id: Specific workflow to analyze (None for global analytics)
            
        Returns:
            Dictionary containing analytics data
        """
        try:
            if workflow_id:
                return await self._get_workflow_analytics(workflow_id)
            else:
                return await self._get_global_analytics()
                
        except Exception as e:
            self.log_error(f"Failed to get progress analytics: {e}")
            return {"error": str(e)}
    
    async def stop_workflow_tracking(self, workflow_id: str) -> None:
        """
        Stop tracking progress for a workflow
        
        Args:
            workflow_id: ID of the workflow to stop tracking
        """
        try:
            # Stop monitoring task
            if workflow_id in self.active_monitors:
                self.active_monitors[workflow_id].cancel()
                del self.active_monitors[workflow_id]
            
            # Emit workflow completed event
            if workflow_id in self.workflow_progress:
                progress = self.workflow_progress[workflow_id]
                
                await self._emit_progress_event(ProgressEvent(
                    event_type=ProgressEventType.WORKFLOW_COMPLETED,
                    timestamp=datetime.now(),
                    workflow_id=workflow_id,
                    progress_percentage=progress.progress_percentage,
                    message=f"Workflow completed: {progress.completed_tasks}/{progress.total_tasks} tasks successful",
                    details={
                        "total_tasks": progress.total_tasks,
                        "completed_tasks": progress.completed_tasks,
                        "failed_tasks": progress.failed_tasks,
                        "duration": (datetime.now() - progress.start_time).total_seconds()
                    }
                ))
            
            # Archive progress data
            await self._archive_workflow_progress(workflow_id)
            
            self.log_info(f"Stopped tracking workflow: {workflow_id}")
            
        except Exception as e:
            self.log_error(f"Failed to stop workflow tracking: {e}")
    
    async def _monitor_workflow_progress(self, workflow_id: str) -> None:
        """Monitor workflow progress in real-time"""
        try:
            while workflow_id in self.workflow_progress:
                await self._update_workflow_progress(workflow_id)
                await asyncio.sleep(self.monitoring_interval)
                
        except asyncio.CancelledError:
            self.log_info(f"Progress monitoring cancelled for workflow: {workflow_id}")
        except Exception as e:
            self.log_error(f"Progress monitoring error: {e}")
    
    async def _update_workflow_progress(self, workflow_id: str) -> None:
        """Update overall workflow progress based on task progress"""
        try:
            if workflow_id not in self.workflow_progress:
                return
            
            progress = self.workflow_progress[workflow_id]
            
            # Count task statuses
            completed_count = 0
            failed_count = 0
            in_progress_count = 0
            pending_count = 0
            
            for task_id, task_data in self.task_progress.items():
                if task_data.get("workflow_id") != workflow_id:
                    continue
                
                status = task_data.get("status")
                if status == TaskStatus.COMPLETED.value:
                    completed_count += 1
                elif status == TaskStatus.FAILED.value:
                    failed_count += 1
                elif status == TaskStatus.IN_PROGRESS.value:
                    in_progress_count += 1
                else:
                    pending_count += 1
            
            # Update progress
            progress.completed_tasks = completed_count
            progress.failed_tasks = failed_count
            progress.in_progress_tasks = in_progress_count
            progress.pending_tasks = pending_count
            progress.current_time = datetime.now()
            
            # Calculate progress percentage
            if progress.total_tasks > 0:
                progress.progress_percentage = (completed_count / progress.total_tasks) * 100
            
            # Update estimated completion
            progress.estimated_completion = await self._estimate_completion_time(workflow_id)
            
            # Update current phase
            progress.current_phase = await self._determine_current_phase(workflow_id)
            
        except Exception as e:
            self.log_error(f"Failed to update workflow progress: {e}")
    
    async def _emit_progress_event(self, event: ProgressEvent) -> None:
        """Emit a progress event to all registered callbacks"""
        try:
            # Store event
            self.progress_events.append(event)
            
            # Limit event history
            if len(self.progress_events) > 1000:
                self.progress_events = self.progress_events[-500:]
            
            # Call registered callbacks
            for callback_key, callback_list in self.progress_callbacks.items():
                for callback_info in callback_list:
                    try:
                        # Check if callback applies to this event
                        if callback_info["workflow_id"] != event.workflow_id:
                            continue
                        
                        event_types = callback_info.get("event_types")
                        if event_types and event.event_type not in event_types:
                            continue
                        
                        # Call the callback
                        callback = callback_info["callback"]
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event)
                        else:
                            callback(event)
                            
                    except Exception as e:
                        self.log_warning(f"Progress callback failed: {e}")
            
        except Exception as e:
            self.log_error(f"Failed to emit progress event: {e}")
    
    async def _check_milestones(self, workflow_id: str) -> None:
        """Check if any milestones have been reached"""
        try:
            if workflow_id not in self.milestone_definitions:
                return
            
            progress = self.workflow_progress.get(workflow_id)
            if not progress:
                return
            
            milestones = self.milestone_definitions[workflow_id]
            
            for milestone in milestones:
                milestone_percentage = milestone.get("percentage", 0)
                milestone_name = milestone.get("name", "Unnamed milestone")
                
                # Check if milestone is reached and not already triggered
                if (progress.progress_percentage >= milestone_percentage and 
                    not milestone.get("triggered", False)):
                    
                    milestone["triggered"] = True
                    milestone["triggered_at"] = datetime.now()
                    
                    # Emit milestone event
                    await self._emit_progress_event(ProgressEvent(
                        event_type=ProgressEventType.MILESTONE_REACHED,
                        timestamp=datetime.now(),
                        workflow_id=workflow_id,
                        progress_percentage=progress.progress_percentage,
                        message=f"Milestone reached: {milestone_name}",
                        details=milestone
                    ))
                    
        except Exception as e:
            self.log_error(f"Failed to check milestones: {e}")
    
    async def _calculate_performance_metrics(self, workflow_id: str) -> Dict[str, Any]:
        """Calculate performance metrics for a workflow"""
        try:
            progress = self.workflow_progress.get(workflow_id)
            if not progress:
                return {}
            
            # Calculate duration
            duration = (progress.current_time - progress.start_time).total_seconds()
            
            # Calculate throughput
            throughput = progress.completed_tasks / duration if duration > 0 else 0
            
            # Calculate success rate
            total_finished = progress.completed_tasks + progress.failed_tasks
            success_rate = (progress.completed_tasks / total_finished * 100) if total_finished > 0 else 0
            
            # Calculate estimated remaining time
            remaining_tasks = progress.total_tasks - progress.completed_tasks - progress.failed_tasks
            estimated_remaining = remaining_tasks / throughput if throughput > 0 else None
            
            return {
                "duration_seconds": duration,
                "throughput_tasks_per_second": throughput,
                "success_rate_percentage": success_rate,
                "estimated_remaining_seconds": estimated_remaining,
                "tasks_per_minute": throughput * 60,
                "completion_velocity": progress.progress_percentage / duration if duration > 0 else 0
            }
            
        except Exception as e:
            self.log_error(f"Failed to calculate performance metrics: {e}")
            return {}
    
    async def _estimate_completion_time(self, workflow_id: str) -> Optional[datetime]:
        """Estimate workflow completion time"""
        try:
            progress = self.workflow_progress.get(workflow_id)
            if not progress or progress.completed_tasks == 0:
                return None
            
            # Calculate average time per task
            duration = (progress.current_time - progress.start_time).total_seconds()
            avg_time_per_task = duration / progress.completed_tasks
            
            # Estimate remaining time
            remaining_tasks = progress.total_tasks - progress.completed_tasks - progress.failed_tasks
            estimated_remaining_seconds = remaining_tasks * avg_time_per_task
            
            return progress.current_time + timedelta(seconds=estimated_remaining_seconds)
            
        except Exception as e:
            self.log_error(f"Failed to estimate completion time: {e}")
            return None
    
    async def _determine_current_phase(self, workflow_id: str) -> str:
        """Determine the current phase of workflow execution"""
        try:
            progress = self.workflow_progress.get(workflow_id)
            if not progress:
                return "unknown"
            
            if progress.progress_percentage < 10:
                return "initialization"
            elif progress.progress_percentage < 30:
                return "early_execution"
            elif progress.progress_percentage < 70:
                return "main_execution"
            elif progress.progress_percentage < 90:
                return "late_execution"
            else:
                return "finalization"
                
        except Exception as e:
            self.log_error(f"Failed to determine current phase: {e}")
            return "unknown"
    
    async def _get_workflow_analytics(self, workflow_id: str) -> Dict[str, Any]:
        """Get analytics for a specific workflow"""
        try:
            progress = self.workflow_progress.get(workflow_id)
            if not progress:
                return {"error": "Workflow not found"}
            
            # Get performance metrics
            performance_metrics = await self._calculate_performance_metrics(workflow_id)
            
            # Get event statistics
            events = await self.get_progress_events(workflow_id=workflow_id)
            event_counts = {}
            for event in events:
                event_type = event.event_type.value
                event_counts[event_type] = event_counts.get(event_type, 0) + 1
            
            return {
                "workflow_id": workflow_id,
                "progress": progress.to_dict(),
                "performance_metrics": performance_metrics,
                "event_statistics": event_counts,
                "total_events": len(events),
                "milestones_reached": sum(1 for m in progress.milestones if m.get("triggered", False))
            }
            
        except Exception as e:
            self.log_error(f"Failed to get workflow analytics: {e}")
            return {"error": str(e)}
    
    async def _get_global_analytics(self) -> Dict[str, Any]:
        """Get global analytics across all workflows"""
        try:
            total_workflows = len(self.workflow_progress)
            total_events = len(self.progress_events)
            
            # Calculate aggregate metrics
            total_tasks = sum(p.total_tasks for p in self.workflow_progress.values())
            total_completed = sum(p.completed_tasks for p in self.workflow_progress.values())
            total_failed = sum(p.failed_tasks for p in self.workflow_progress.values())
            
            global_success_rate = (total_completed / (total_completed + total_failed) * 100) if (total_completed + total_failed) > 0 else 0
            
            return {
                "total_workflows": total_workflows,
                "total_tasks": total_tasks,
                "total_completed_tasks": total_completed,
                "total_failed_tasks": total_failed,
                "global_success_rate": global_success_rate,
                "total_events": total_events,
                "active_workflows": len([p for p in self.workflow_progress.values() if p.progress_percentage < 100])
            }
            
        except Exception as e:
            self.log_error(f"Failed to get global analytics: {e}")
            return {"error": str(e)}
    
    async def _archive_workflow_progress(self, workflow_id: str) -> None:
        """Archive completed workflow progress data"""
        try:
            if workflow_id in self.workflow_progress:
                progress = self.workflow_progress[workflow_id]
                
                # Store in performance history
                if workflow_id not in self.performance_history:
                    self.performance_history[workflow_id] = []
                
                self.performance_history[workflow_id].append({
                    "completed_at": datetime.now().isoformat(),
                    "progress_data": progress.to_dict(),
                    "performance_metrics": await self._calculate_performance_metrics(workflow_id)
                })
                
                # Remove from active tracking
                del self.workflow_progress[workflow_id]
                
                # Clean up related data
                if workflow_id in self.milestone_definitions:
                    del self.milestone_definitions[workflow_id]
                
                # Remove task progress for this workflow
                tasks_to_remove = [
                    task_id for task_id, task_data in self.task_progress.items()
                    if task_data.get("workflow_id") == workflow_id
                ]
                for task_id in tasks_to_remove:
                    del self.task_progress[task_id]
                
        except Exception as e:
            self.log_error(f"Failed to archive workflow progress: {e}")
    
    async def cleanup(self):
        """Cleanup progress tracker resources"""
        try:
            # Cancel all monitoring tasks
            for monitor_task in self.active_monitors.values():
                monitor_task.cancel()
            
            # Wait for tasks to complete
            if self.active_monitors:
                await asyncio.gather(*self.active_monitors.values(), return_exceptions=True)
            
            # Clear data structures
            self.workflow_progress.clear()
            self.task_progress.clear()
            self.progress_events.clear()
            self.progress_callbacks.clear()
            self.milestone_definitions.clear()
            self.performance_history.clear()
            self.active_monitors.clear()
            
            self.log_info("Task progress tracker cleanup completed")
            
        except Exception as e:
            self.log_error(f"Task progress tracker cleanup failed: {e}")


# Global task progress tracker instance
task_progress_tracker = TaskProgressTracker()
