"""
Test script for Tier-Based Access Control System
Tests subscription tiers, quota management, and feature access control
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any

from app.services.access_control import (
    access_control_manager, SubscriptionTier, FeatureType, QuotaType
)


async def test_subscription_tiers():
    """Test subscription tier configurations and limits"""
    
    print("🎯 Testing Subscription Tiers")
    print("=" * 50)
    
    try:
        # Test 1: Tier Limit Configurations
        print("\n1️⃣ Testing Tier Limit Configurations...")
        
        for tier in SubscriptionTier:
            tier_limits = access_control_manager.tier_limits[tier]
            
            print(f"\n   📊 {tier.value.upper()} Tier:")
            print(f"      Monthly browsing tasks: {tier_limits.monthly_browsing_tasks}")
            print(f"      Concurrent sessions: {tier_limits.concurrent_sessions}")
            print(f"      Max workflow complexity: {tier_limits.max_workflow_complexity}")
            print(f"      Storage: {tier_limits.storage_mb} MB")
            print(f"      Verification requests: {tier_limits.verification_requests}")
            print(f"      Custom workflows: {tier_limits.custom_workflows}")
            print(f"      API calls/hour: {tier_limits.api_calls_per_hour}")
            print(f"      Features: {len(tier_limits.features)}")
            print(f"      Support level: {tier_limits.support_level}")
        
        # Test 2: Feature Access by Tier
        print("\n2️⃣ Testing Feature Access by Tier...")
        
        test_features = [
            FeatureType.BASIC_BROWSING,
            FeatureType.ADVANCED_EXTRACTION,
            FeatureType.VERIFICATION,
            FeatureType.PARALLEL_PROCESSING,
            FeatureType.CUSTOM_WORKFLOWS,
            FeatureType.BULK_OPERATIONS
        ]
        
        for feature in test_features:
            print(f"\n   🔧 {feature.value}:")
            
            for tier in SubscriptionTier:
                tier_limits = access_control_manager.tier_limits[tier]
                has_access = feature in tier_limits.features
                
                status = "✅" if has_access else "❌"
                print(f"      {status} {tier.value}")
        
        print("\n✅ Subscription tier testing completed")
        
    except Exception as e:
        print(f"❌ Subscription tier testing failed: {e}")


async def test_access_control():
    """Test access control functionality"""
    
    print("\n🔐 Testing Access Control")
    print("-" * 40)
    
    try:
        # Test 1: Feature Access Check
        print("\n1️⃣ Testing Feature Access Check...")
        
        test_users = [
            ("user_free", SubscriptionTier.FREE),
            ("user_starter", SubscriptionTier.STARTER),
            ("user_pro", SubscriptionTier.PRO),
            ("user_enterprise", SubscriptionTier.ENTERPRISE)
        ]
        
        # Create test users with different tiers
        for user_id, tier in test_users:
            # Simulate user creation with specific tier
            user_quota = await access_control_manager._create_user_quota(user_id)
            user_quota.tier = tier
            
            # Update limits based on tier
            tier_limits = access_control_manager.tier_limits[tier]
            user_quota.limits = {
                QuotaType.BROWSING_TASKS: tier_limits.monthly_browsing_tasks,
                QuotaType.CONCURRENT_SESSIONS: tier_limits.concurrent_sessions,
                QuotaType.STORAGE_MB: tier_limits.storage_mb,
                QuotaType.VERIFICATION_REQUESTS: tier_limits.verification_requests,
                QuotaType.CUSTOM_WORKFLOWS: tier_limits.custom_workflows,
                QuotaType.API_CALLS: tier_limits.api_calls_per_hour
            }
            
            access_control_manager.user_quotas[user_id] = user_quota
        
        # Test access for different features
        test_feature = FeatureType.BASIC_BROWSING
        
        for user_id, tier in test_users:
            access_result = await access_control_manager.check_access(
                user_id=user_id,
                feature=test_feature
            )
            
            status = "✅" if access_result["access_granted"] else "❌"
            print(f"   {status} {user_id} ({tier.value}): {test_feature.value}")
            
            if not access_result["access_granted"]:
                print(f"      Reason: {access_result['reason']}")
                print(f"      Required tier: {access_result.get('required_tier', 'unknown')}")
        
        # Test 2: Quota Consumption
        print("\n2️⃣ Testing Quota Consumption...")
        
        # Test browsing quota consumption for starter user
        user_id = "user_starter"
        
        print(f"   Testing quota consumption for {user_id}...")
        
        # Check initial quota
        initial_quota = await access_control_manager.check_browsing_quota(user_id)
        print(f"   Initial browsing tasks: {initial_quota['remaining_tasks']}")
        
        # Consume some quota
        for i in range(3):
            consumption_result = await access_control_manager.consume_quota(
                user_id=user_id,
                quota_type=QuotaType.BROWSING_TASKS,
                amount=1,
                metadata={"test_task": f"task_{i+1}"}
            )
            
            if consumption_result["success"]:
                print(f"   ✅ Consumed 1 browsing task. Remaining: {consumption_result['remaining']}")
            else:
                print(f"   ❌ Quota consumption failed: {consumption_result['reason']}")
        
        # Check quota after consumption
        final_quota = await access_control_manager.check_browsing_quota(user_id)
        print(f"   Final browsing tasks: {final_quota['remaining_tasks']}")
        
        # Test 3: Quota Exceeded Scenario
        print("\n3️⃣ Testing Quota Exceeded Scenario...")
        
        # Try to consume more than available
        user_quota = access_control_manager.user_quotas[user_id]
        remaining = user_quota.limits[QuotaType.BROWSING_TASKS] - user_quota.usage[QuotaType.BROWSING_TASKS]
        
        print(f"   Attempting to consume {remaining + 5} tasks (exceeds limit)...")
        
        consumption_result = await access_control_manager.consume_quota(
            user_id=user_id,
            quota_type=QuotaType.BROWSING_TASKS,
            amount=remaining + 5
        )
        
        if not consumption_result["success"]:
            print(f"   ✅ Quota exceeded correctly blocked: {consumption_result['reason']}")
            print(f"   Overage: {consumption_result.get('overage', 0)}")
        else:
            print(f"   ❌ Quota exceeded not blocked (unexpected)")
        
        # Test 4: User Limits Retrieval
        print("\n4️⃣ Testing User Limits Retrieval...")
        
        for user_id, tier in test_users[:2]:  # Test first 2 users
            user_limits = await access_control_manager.get_user_limits(user_id)
            
            if "error" not in user_limits:
                print(f"   ✅ {user_id} ({tier.value}):")
                print(f"      Tier: {user_limits['subscription_tier']}")
                print(f"      Browsing tasks: {user_limits['current_usage']['browsing_tasks']}/{user_limits['limits']['monthly_browsing_tasks']}")
                print(f"      Features: {len(user_limits['available_features'])}")
                print(f"      Support: {user_limits['support_level']}")
            else:
                print(f"   ❌ {user_id}: {user_limits['error']}")
        
        print("\n✅ Access control testing completed")
        
    except Exception as e:
        print(f"❌ Access control testing failed: {e}")


async def test_tier_upgrades():
    """Test tier upgrade simulations"""
    
    print("\n⬆️ Testing Tier Upgrades")
    print("-" * 40)
    
    try:
        # Test 1: Upgrade Simulation
        print("\n1️⃣ Testing Upgrade Simulation...")
        
        upgrade_scenarios = [
            ("user_free", SubscriptionTier.STARTER),
            ("user_starter", SubscriptionTier.PRO),
            ("user_pro", SubscriptionTier.ENTERPRISE)
        ]
        
        for user_id, target_tier in upgrade_scenarios:
            if user_id in access_control_manager.user_quotas:
                current_tier = access_control_manager.user_quotas[user_id].tier
                
                upgrade_simulation = await access_control_manager.upgrade_tier_simulation(
                    user_id=user_id,
                    target_tier=target_tier
                )
                
                print(f"\n   📈 {user_id}: {current_tier.value} → {target_tier.value}")
                print(f"      Browsing tasks: {upgrade_simulation['improvements']['monthly_browsing_tasks']['current']} → {upgrade_simulation['improvements']['monthly_browsing_tasks']['upgraded']}")
                print(f"      Concurrent sessions: {upgrade_simulation['improvements']['concurrent_sessions']['current']} → {upgrade_simulation['improvements']['concurrent_sessions']['upgraded']}")
                print(f"      New features: {len(upgrade_simulation['new_features'])}")
                print(f"      Support upgrade: {upgrade_simulation['support_upgrade']}")
        
        # Test 2: Upgrade Opportunities
        print("\n2️⃣ Testing Upgrade Opportunities...")
        
        # Simulate high usage for starter user
        user_id = "user_starter"
        if user_id in access_control_manager.user_quotas:
            user_quota = access_control_manager.user_quotas[user_id]
            
            # Set high usage (90% of quota)
            browsing_limit = user_quota.limits[QuotaType.BROWSING_TASKS]
            user_quota.usage[QuotaType.BROWSING_TASKS] = int(browsing_limit * 0.9)
            
            # Add some overages
            user_quota.overages[QuotaType.VERIFICATION_REQUESTS] = 5
        
        # Get upgrade opportunities
        analytics = await access_control_manager.get_access_analytics()
        upgrade_opportunities = analytics.get("upgrade_opportunities", [])
        
        print(f"   Found {len(upgrade_opportunities)} upgrade opportunities:")
        
        for opportunity in upgrade_opportunities:
            print(f"   📊 User: {opportunity['user_id']}")
            print(f"      Current tier: {opportunity['current_tier']}")
            print(f"      High usage quotas: {opportunity['high_usage_quotas']}")
            print(f"      Has overages: {opportunity['has_overages']}")
            print(f"      Recommended tier: {opportunity['recommended_tier']}")
        
        print("\n✅ Tier upgrade testing completed")
        
    except Exception as e:
        print(f"❌ Tier upgrade testing failed: {e}")


async def test_analytics():
    """Test access control analytics"""
    
    print("\n📊 Testing Analytics")
    print("-" * 40)
    
    try:
        # Test 1: Access Analytics
        print("\n1️⃣ Testing Access Analytics...")
        
        analytics = await access_control_manager.get_access_analytics()
        
        print("   📈 Usage Statistics:")
        usage_stats = analytics.get("usage_statistics", {})
        print(f"      Total requests: {usage_stats.get('total_requests', 0)}")
        print(f"      Blocked requests: {usage_stats.get('blocked_requests', 0)}")
        print(f"      Quota exceeded: {usage_stats.get('quota_exceeded', 0)}")
        print(f"      Feature restricted: {usage_stats.get('feature_restricted', 0)}")
        
        if usage_stats.get('total_requests', 0) > 0:
            print(f"      Block rate: {usage_stats.get('block_rate', 0):.1f}%")
            print(f"      Quota exceeded rate: {usage_stats.get('quota_exceeded_rate', 0):.1f}%")
        
        print("\n   🎯 Tier Distribution:")
        tier_distribution = analytics.get("tier_distribution", {})
        for tier, count in tier_distribution.items():
            print(f"      {tier}: {count} users")
        
        print("\n   📊 Quota Utilization:")
        quota_utilization = analytics.get("quota_utilization", {})
        for quota_type, utilization in quota_utilization.items():
            print(f"      {quota_type}: {utilization:.1f}% average")
        
        # Test 2: Feature Usage Analytics
        print("\n2️⃣ Testing Feature Usage Analytics...")
        
        feature_usage = analytics.get("feature_usage", {})
        
        if feature_usage:
            print("   🔧 Feature Usage:")
            for feature, count in feature_usage.items():
                print(f"      {feature}: {count} uses")
        else:
            print("   📝 No feature usage data available")
        
        print("\n✅ Analytics testing completed")
        
    except Exception as e:
        print(f"❌ Analytics testing failed: {e}")


async def test_integration_scenarios():
    """Test real-world integration scenarios"""
    
    print("\n🔗 Testing Integration Scenarios")
    print("-" * 40)
    
    try:
        # Scenario 1: Free User Trying Browser Automation
        print("\n1️⃣ Free User Trying Browser Automation...")
        
        user_id = "user_free"
        access_result = await access_control_manager.check_access(
            user_id=user_id,
            feature=FeatureType.BASIC_BROWSING
        )
        
        print(f"   📊 Free user browser access: {'✅' if access_result['access_granted'] else '❌'}")
        if not access_result["access_granted"]:
            print(f"      Reason: {access_result['reason']}")
            print(f"      Required tier: {access_result.get('required_tier')}")
            print("      → User should be prompted to upgrade to Starter")
        
        # Scenario 2: Starter User Approaching Quota Limit
        print("\n2️⃣ Starter User Approaching Quota Limit...")
        
        user_id = "user_starter"
        quota_status = await access_control_manager.check_browsing_quota(user_id)
        
        print(f"   📊 Starter user quota status:")
        print(f"      Can browse: {quota_status['can_browse']}")
        print(f"      Remaining tasks: {quota_status['remaining_tasks']}")
        print(f"      Usage: {quota_status.get('percentage_used', 0):.1f}%")
        
        if quota_status.get('percentage_used', 0) > 80:
            print("      ⚠️ User should be notified about approaching limit")
            print("      → Suggest upgrade to Pro for more tasks")
        
        # Scenario 3: Pro User Using Advanced Features
        print("\n3️⃣ Pro User Using Advanced Features...")
        
        user_id = "user_pro"
        advanced_features = [
            FeatureType.PARALLEL_PROCESSING,
            FeatureType.CUSTOM_WORKFLOWS,
            FeatureType.ADVANCED_MEMORY
        ]
        
        for feature in advanced_features:
            access_result = await access_control_manager.check_access(
                user_id=user_id,
                feature=feature
            )
            
            status = "✅" if access_result["access_granted"] else "❌"
            print(f"   {status} {feature.value}")
        
        # Scenario 4: Enterprise User with Unlimited Access
        print("\n4️⃣ Enterprise User with Unlimited Access...")
        
        user_id = "user_enterprise"
        user_limits = await access_control_manager.get_user_limits(user_id)
        
        print(f"   📊 Enterprise user limits:")
        print(f"      Monthly browsing tasks: {user_limits['limits']['monthly_browsing_tasks']}")
        print(f"      Concurrent sessions: {user_limits['limits']['concurrent_sessions']}")
        print(f"      Max workflow complexity: {user_limits['limits']['max_workflow_complexity']}")
        print(f"      Available features: {len(user_limits['available_features'])}")
        print(f"      Support level: {user_limits['support_level']}")
        
        # Scenario 5: Quota Reset Simulation
        print("\n5️⃣ Quota Reset Simulation...")
        
        user_id = "user_starter"
        user_quota = access_control_manager.user_quotas[user_id]
        
        print(f"   Before reset - Usage: {user_quota.usage[QuotaType.BROWSING_TASKS]}")
        print(f"   Period end: {user_quota.current_period_end}")
        
        # Simulate period end
        user_quota.current_period_end = datetime.now() - timedelta(days=1)
        
        # Trigger quota check (which should reset)
        await access_control_manager._get_user_quota(user_id)
        
        print(f"   After reset - Usage: {user_quota.usage[QuotaType.BROWSING_TASKS]}")
        print(f"   New period end: {user_quota.current_period_end}")
        print("   ✅ Quota reset successfully")
        
        print("\n✅ All integration scenarios completed")
        
    except Exception as e:
        print(f"❌ Integration scenarios testing failed: {e}")


if __name__ == "__main__":
    print("🚀 Tier-Based Access Control Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    asyncio.run(test_subscription_tiers())
    asyncio.run(test_access_control())
    asyncio.run(test_tier_upgrades())
    asyncio.run(test_analytics())
    asyncio.run(test_integration_scenarios())
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 All tier-based access control tests completed!")
    print("\n📝 Summary:")
    print("   ✅ Subscription Tiers - Feature and quota configurations")
    print("   ✅ Access Control - Feature access and quota enforcement")
    print("   ✅ Tier Upgrades - Upgrade simulations and opportunities")
    print("   ✅ Analytics - Usage statistics and insights")
    print("   ✅ Integration Scenarios - Real-world usage patterns")
    print("\n🔧 Key Features Tested:")
    print("   • Tier-based feature access control")
    print("   • Quota tracking and enforcement")
    print("   • Automatic quota resets")
    print("   • Upgrade recommendations")
    print("   • Usage analytics and monitoring")
