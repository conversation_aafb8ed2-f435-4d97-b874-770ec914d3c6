"""
Test script for API Endpoints and Integration
Tests all browser automation API endpoints and chat integration
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any
import httpx

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_123"
TEST_CONVERSATION_ID = "conv_456"
TEST_MESSAGE_ID = "msg_789"


async def test_health_endpoints():
    """Test health check endpoints"""
    
    print("🏥 Testing Health Endpoints")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test basic health check
            print("\n1️⃣ Testing Basic Health Check...")
            response = await client.get(f"{BASE_URL}/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"   ✅ Health Status: {health_data['status']}")
                print(f"   📅 Timestamp: {health_data['timestamp']}")
                print(f"   🔧 Service: {health_data['service']}")
            else:
                print(f"   ❌ Health check failed: {response.status_code}")
            
            # Test detailed health check
            print("\n2️⃣ Testing Detailed Health Check...")
            response = await client.get(f"{BASE_URL}/health/detailed")
            
            if response.status_code == 200:
                detailed_health = response.json()
                print(f"   ✅ Overall Status: {detailed_health['status']}")
                print(f"   ⏱️ Uptime: {detailed_health.get('uptime_seconds', 0):.1f}s")
                
                services = detailed_health.get('services', {})
                for service_name, service_status in services.items():
                    status_icon = "✅" if service_status.get('healthy', False) else "❌"
                    print(f"   {status_icon} {service_name}: {service_status.get('status', 'unknown')}")
            else:
                print(f"   ❌ Detailed health check failed: {response.status_code}")
            
            # Test readiness check
            print("\n3️⃣ Testing Readiness Check...")
            response = await client.get(f"{BASE_URL}/health/ready")
            
            if response.status_code == 200:
                print("   ✅ Service is ready")
            else:
                print(f"   ❌ Service not ready: {response.status_code}")
            
            # Test service info
            print("\n4️⃣ Testing Service Info...")
            response = await client.get(f"{BASE_URL}/info")
            
            if response.status_code == 200:
                info_data = response.json()
                print(f"   ✅ Service: {info_data['service']}")
                print(f"   📦 Version: {info_data['version']}")
                print(f"   🔧 Features: {len(info_data.get('features', []))}")
            else:
                print(f"   ❌ Service info failed: {response.status_code}")
        
        print("\n✅ Health endpoints testing completed")
        
    except Exception as e:
        print(f"❌ Health endpoints testing failed: {e}")


async def test_quota_dashboard_endpoints():
    """Test quota dashboard endpoints"""
    
    print("\n📊 Testing Quota Dashboard Endpoints")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test quota usage
            print("\n1️⃣ Testing Quota Usage...")
            response = await client.get(f"{BASE_URL}/api/quota/usage/{TEST_USER_ID}")
            
            if response.status_code == 200:
                quota_data = response.json()
                print(f"   ✅ User Tier: {quota_data.get('subscription_tier', 'unknown')}")
                print(f"   📈 Browsing Tasks: {quota_data.get('current_usage', {}).get('browsing_tasks', 0)}")
                print(f"   🎯 Features: {len(quota_data.get('available_features', []))}")
            else:
                print(f"   ❌ Quota usage failed: {response.status_code}")
            
            # Test browsing status
            print("\n2️⃣ Testing Browsing Status...")
            response = await client.get(f"{BASE_URL}/api/quota/browsing-status/{TEST_USER_ID}")
            
            if response.status_code == 200:
                browsing_data = response.json()
                print(f"   ✅ Can Browse: {browsing_data.get('can_browse', False)}")
                print(f"   📊 Remaining Tasks: {browsing_data.get('remaining_tasks', 0)}")
                print(f"   📅 Reset Date: {browsing_data.get('reset_date', 'unknown')}")
            else:
                print(f"   ❌ Browsing status failed: {response.status_code}")
            
            # Test feature access check
            print("\n3️⃣ Testing Feature Access Check...")
            access_request = {
                "feature": "basic_browsing",
                "resource_requirements": {"browsing_tasks": 1}
            }
            
            response = await client.post(
                f"{BASE_URL}/api/quota/check-access/{TEST_USER_ID}",
                json=access_request
            )
            
            if response.status_code == 200:
                access_data = response.json()
                print(f"   ✅ Access Granted: {access_data.get('access_granted', False)}")
                print(f"   🎯 Feature: {access_data.get('feature', 'unknown')}")
                print(f"   📊 Tier: {access_data.get('tier', 'unknown')}")
            else:
                print(f"   ❌ Feature access check failed: {response.status_code}")
            
            # Test tier information
            print("\n4️⃣ Testing Tier Information...")
            response = await client.get(f"{BASE_URL}/api/quota/tiers")
            
            if response.status_code == 200:
                tiers_data = response.json()
                tiers = tiers_data.get('tiers', {})
                print(f"   ✅ Available Tiers: {len(tiers)}")
                
                for tier_name, tier_info in tiers.items():
                    print(f"   📊 {tier_name.upper()}: {tier_info.get('monthly_browsing_tasks', 0)} tasks/month")
            else:
                print(f"   ❌ Tier information failed: {response.status_code}")
        
        print("\n✅ Quota dashboard endpoints testing completed")
        
    except Exception as e:
        print(f"❌ Quota dashboard endpoints testing failed: {e}")


async def test_chat_integration_endpoints():
    """Test chat integration endpoints"""
    
    print("\n💬 Testing Chat Integration Endpoints")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test capability check
            print("\n1️⃣ Testing Browser Capability Check...")
            capability_request = {
                "user_id": TEST_USER_ID,
                "requested_action": "web_search",
                "context": {"current_url": "https://example.com"}
            }
            
            response = await client.post(
                f"{BASE_URL}/api/chat-integration/check-capability",
                json=capability_request
            )
            
            if response.status_code == 200:
                capability_data = response.json()
                print(f"   ✅ Browsing Enabled: {capability_data.get('browsing_enabled', False)}")
                print(f"   🎯 Action Supported: {capability_data.get('action_supported', {}).get('supported', False)}")
                print(f"   📊 Tier: {capability_data.get('tier', 'unknown')}")
            else:
                print(f"   ❌ Capability check failed: {response.status_code}")
            
            # Test intent detection
            print("\n2️⃣ Testing Intent Detection...")
            intent_request = {
                "user_message": "Can you search for information about Python programming?",
                "conversation_context": {"topic": "programming"}
            }
            
            response = await client.post(
                f"{BASE_URL}/api/chat-integration/intent-detection",
                json=intent_request
            )
            
            if response.status_code == 200:
                intent_data = response.json()
                print(f"   ✅ Browser Automation Required: {intent_data.get('browser_automation_required', False)}")
                print(f"   🎯 Primary Intent: {intent_data.get('primary_intent', 'none')}")
                print(f"   📊 Detected Intents: {len(intent_data.get('detected_intents', []))}")
                print(f"   🔧 Recommended Action: {intent_data.get('recommended_action', {}).get('action', 'none')}")
            else:
                print(f"   ❌ Intent detection failed: {response.status_code}")
            
            # Test chat browser execution (if browsing is enabled)
            print("\n3️⃣ Testing Chat Browser Execution...")
            chat_browser_request = {
                "user_id": TEST_USER_ID,
                "conversation_id": TEST_CONVERSATION_ID,
                "message_id": TEST_MESSAGE_ID,
                "user_message": "Search for information about FastAPI",
                "detected_intent": "web_search",
                "extracted_parameters": {"search_query": "FastAPI"},
                "context": {},
                "routing_strategy": "intelligent",
                "roles": ["web_navigator"]
            }
            
            response = await client.post(
                f"{BASE_URL}/api/chat-integration/execute-from-chat",
                json=chat_browser_request
            )
            
            if response.status_code == 200:
                execution_data = response.json()
                print(f"   ✅ Task Status: {execution_data.get('status', 'unknown')}")
                print(f"   🆔 Task ID: {execution_data.get('task_id', 'none')}")
                print(f"   💬 Response: {execution_data.get('response_message', 'none')[:50]}...")
                
                # If task was created, test status check
                task_id = execution_data.get('task_id')
                if task_id:
                    print("\n4️⃣ Testing Chat Task Status...")
                    response = await client.get(
                        f"{BASE_URL}/api/chat-integration/chat-task/{TEST_CONVERSATION_ID}/{TEST_MESSAGE_ID}"
                    )
                    
                    if response.status_code == 200:
                        status_data = response.json()
                        print(f"   ✅ Chat Task Status: {status_data.get('status', 'unknown')}")
                        print(f"   📊 Progress: {status_data.get('progress', 0):.1f}%")
                    else:
                        print(f"   ❌ Chat task status failed: {response.status_code}")
            else:
                print(f"   ❌ Chat browser execution failed: {response.status_code}")
                if response.status_code == 403:
                    print("   ℹ️ This is expected if user doesn't have browsing access")
        
        print("\n✅ Chat integration endpoints testing completed")
        
    except Exception as e:
        print(f"❌ Chat integration endpoints testing failed: {e}")


async def test_browser_automation_endpoints():
    """Test browser automation endpoints"""
    
    print("\n🌐 Testing Browser Automation Endpoints")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test navigation endpoint
            print("\n1️⃣ Testing Navigation Endpoint...")
            navigation_request = {
                "user_id": TEST_USER_ID,
                "task_description": "Navigate to example.com and extract the title",
                "target_url": "https://example.com",
                "workflow_type": "sequential",
                "max_roles": 3,
                "timeout_seconds": 300,
                "verify_results": True,
                "save_screenshots": False
            }
            
            response = await client.post(
                f"{BASE_URL}/api/browser/navigate",
                json=navigation_request
            )
            
            if response.status_code == 200:
                nav_data = response.json()
                print(f"   ✅ Task Created: {nav_data.get('task_id', 'none')}")
                print(f"   📊 Status: {nav_data.get('status', 'unknown')}")
                print(f"   👤 User: {nav_data.get('user_id', 'unknown')}")
                
                # Test task status
                task_id = nav_data.get('task_id')
                if task_id:
                    print("\n2️⃣ Testing Task Status...")
                    await asyncio.sleep(1)  # Wait a moment
                    
                    response = await client.get(f"{BASE_URL}/api/browser/task/{task_id}")
                    
                    if response.status_code == 200:
                        status_data = response.json()
                        print(f"   ✅ Task Status: {status_data.get('status', 'unknown')}")
                        print(f"   📊 Progress: {status_data.get('progress', 0):.1f}%")
                    else:
                        print(f"   ❌ Task status failed: {response.status_code}")
            else:
                print(f"   ❌ Navigation endpoint failed: {response.status_code}")
                if response.status_code in [402, 403]:
                    print("   ℹ️ This is expected if user doesn't have browsing access or quota")
            
            # Test user tasks endpoint
            print("\n3️⃣ Testing User Tasks...")
            response = await client.get(f"{BASE_URL}/api/browser/tasks/{TEST_USER_ID}")
            
            if response.status_code == 200:
                tasks_data = response.json()
                print(f"   ✅ Total Tasks: {tasks_data.get('total_count', 0)}")
                print(f"   📊 Returned Tasks: {len(tasks_data.get('tasks', []))}")
                print(f"   👤 User: {tasks_data.get('user_id', 'unknown')}")
            else:
                print(f"   ❌ User tasks failed: {response.status_code}")
            
            # Test extraction endpoint
            print("\n4️⃣ Testing Data Extraction Endpoint...")
            extraction_request = {
                "user_id": TEST_USER_ID,
                "task_description": "Extract product information from the page",
                "target_url": "https://example.com",
                "workflow_type": "sequential",
                "max_roles": 3,
                "verify_results": True
            }
            
            response = await client.post(
                f"{BASE_URL}/api/browser/extract",
                json=extraction_request
            )
            
            if response.status_code == 200:
                extract_data = response.json()
                print(f"   ✅ Extraction Task Created: {extract_data.get('task_id', 'none')}")
                print(f"   📊 Status: {extract_data.get('status', 'unknown')}")
            else:
                print(f"   ❌ Extraction endpoint failed: {response.status_code}")
                if response.status_code in [402, 403]:
                    print("   ℹ️ This is expected if user doesn't have extraction access")
        
        print("\n✅ Browser automation endpoints testing completed")
        
    except Exception as e:
        print(f"❌ Browser automation endpoints testing failed: {e}")


async def test_error_scenarios():
    """Test error handling scenarios"""
    
    print("\n⚠️ Testing Error Scenarios")
    print("-" * 40)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test invalid user ID
            print("\n1️⃣ Testing Invalid User ID...")
            response = await client.get(f"{BASE_URL}/api/quota/usage/invalid_user_123")
            
            if response.status_code == 404:
                print("   ✅ Correctly returned 404 for invalid user")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
            
            # Test invalid task ID
            print("\n2️⃣ Testing Invalid Task ID...")
            response = await client.get(f"{BASE_URL}/api/browser/task/invalid_task_123")
            
            if response.status_code == 404:
                print("   ✅ Correctly returned 404 for invalid task")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
            
            # Test malformed request
            print("\n3️⃣ Testing Malformed Request...")
            malformed_request = {"invalid": "data"}
            
            response = await client.post(
                f"{BASE_URL}/api/browser/navigate",
                json=malformed_request
            )
            
            if response.status_code == 422:
                print("   ✅ Correctly returned 422 for malformed request")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
            
            # Test non-existent endpoint
            print("\n4️⃣ Testing Non-existent Endpoint...")
            response = await client.get(f"{BASE_URL}/api/nonexistent")
            
            if response.status_code == 404:
                print("   ✅ Correctly returned 404 for non-existent endpoint")
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
        
        print("\n✅ Error scenarios testing completed")
        
    except Exception as e:
        print(f"❌ Error scenarios testing failed: {e}")


async def run_all_tests():
    """Run all API endpoint tests"""
    
    print("🚀 API Endpoints and Integration Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"👤 Test User: {TEST_USER_ID}")
    
    try:
        # Run all test suites
        await test_health_endpoints()
        await test_quota_dashboard_endpoints()
        await test_chat_integration_endpoints()
        await test_browser_automation_endpoints()
        await test_error_scenarios()
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 All API endpoint tests completed!")
        
        print("\n📝 Summary:")
        print("   ✅ Health Endpoints - Service status and monitoring")
        print("   ✅ Quota Dashboard - Subscription and usage management")
        print("   ✅ Chat Integration - Natural language browser automation")
        print("   ✅ Browser Automation - Core automation functionality")
        print("   ✅ Error Scenarios - Error handling and validation")
        
        print("\n🔧 Key Features Tested:")
        print("   • Health checks and service monitoring")
        print("   • Tier-based access control and quotas")
        print("   • Chat-to-browser automation integration")
        print("   • Browser task creation and management")
        print("   • Real-time task status and progress")
        print("   • Error handling and validation")
        print("   • Intent detection and capability checking")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")


if __name__ == "__main__":
    print("📋 Note: Make sure the browser automation service is running on localhost:8000")
    print("🚀 Starting API endpoint tests...")
    
    asyncio.run(run_all_tests())
