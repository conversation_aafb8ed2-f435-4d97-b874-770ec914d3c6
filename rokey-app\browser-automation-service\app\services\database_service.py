"""
Database Service for Browser Automation
Handles all database operations for browser automation tasks, usage tracking, and analytics
"""

import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID
import asyncpg
from supabase import create_client, Client
from app.core.logging import LoggerMixin


class DatabaseService(LoggerMixin):
    """Database service for browser automation operations"""
    
    def __init__(self):
        self.supabase: Optional[Client] = None
        self.pg_pool: Optional[asyncpg.Pool] = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize database clients"""
        try:
            # Initialize Supabase client
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            if supabase_url and supabase_key:
                self.supabase = create_client(supabase_url, supabase_key)
                self.log_info("Supabase client initialized")
            else:
                self.log_warning("Supabase credentials not found")
                
        except Exception as e:
            self.log_error(f"Failed to initialize database clients: {e}")
    
    async def create_pg_pool(self):
        """Create PostgreSQL connection pool"""
        try:
            database_url = os.getenv("DATABASE_URL")
            if database_url:
                self.pg_pool = await asyncpg.create_pool(
                    database_url,
                    min_size=2,
                    max_size=10,
                    command_timeout=60
                )
                self.log_info("PostgreSQL pool created")
            else:
                self.log_warning("Database URL not found")
        except Exception as e:
            self.log_error(f"Failed to create PostgreSQL pool: {e}")
    
    async def close_pg_pool(self):
        """Close PostgreSQL connection pool"""
        if self.pg_pool:
            await self.pg_pool.close()
            self.log_info("PostgreSQL pool closed")
    
    async def create_browser_task(
        self,
        user_id: str,
        config_id: str,
        task_description: str,
        task_type: str = "navigation",
        workflow_type: str = "sequential",
        assigned_roles: List[str] = None,
        target_url: str = None,
        max_steps: int = 20,
        timeout_seconds: int = 300,
        verify_results: bool = False,
        save_screenshots: bool = True,
        custom_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a new browser automation task"""
        try:
            task_data = {
                "user_id": user_id,
                "config_id": config_id,
                "task_description": task_description,
                "task_type": task_type,
                "workflow_type": workflow_type,
                "assigned_roles": assigned_roles or [],
                "target_url": target_url,
                "max_steps": max_steps,
                "timeout_seconds": timeout_seconds,
                "verify_results": verify_results,
                "save_screenshots": save_screenshots,
                "custom_config": custom_config or {},
                "status": "pending"
            }
            
            result = self.supabase.table("browser_automation_tasks").insert(task_data).execute()
            
            if result.data:
                task = result.data[0]
                self.log_info(f"Browser task created: {task['id']}")
                return task
            else:
                raise Exception("Failed to create browser task")
                
        except Exception as e:
            self.log_error(f"Error creating browser task: {e}")
            raise
    
    async def update_browser_task(
        self,
        task_id: str,
        updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update a browser automation task"""
        try:
            updates["updated_at"] = datetime.now().isoformat()
            
            result = self.supabase.table("browser_automation_tasks").update(updates).eq("id", task_id).execute()
            
            if result.data:
                task = result.data[0]
                self.log_info(f"Browser task updated: {task_id}")
                return task
            else:
                raise Exception(f"Task not found: {task_id}")
                
        except Exception as e:
            self.log_error(f"Error updating browser task {task_id}: {e}")
            raise
    
    async def get_browser_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get a browser automation task by ID"""
        try:
            result = self.supabase.table("browser_automation_tasks").select("*").eq("id", task_id).execute()
            
            if result.data:
                return result.data[0]
            return None
                
        except Exception as e:
            self.log_error(f"Error getting browser task {task_id}: {e}")
            return None
    
    async def check_user_quota(self, user_id: str, tier: str) -> Dict[str, Any]:
        """Check user's browser automation quota for current month"""
        try:
            current_month = datetime.now().strftime("%Y-%m")
            
            # Get or create usage record for current month
            result = self.supabase.table("browser_automation_usage").select("*").eq("user_id", user_id).eq("month_year", current_month).execute()
            
            if result.data:
                usage = result.data[0]
            else:
                # Create new usage record
                tier_limits = {
                    "free": 0,
                    "starter": 15,
                    "pro": 100,
                    "enterprise": 1000
                }
                
                usage_data = {
                    "user_id": user_id,
                    "month_year": current_month,
                    "tier": tier,
                    "tasks_used": 0,
                    "tasks_limit": tier_limits.get(tier, 0)
                }
                
                create_result = self.supabase.table("browser_automation_usage").insert(usage_data).execute()
                usage = create_result.data[0] if create_result.data else usage_data
            
            return {
                "tasks_used": usage["tasks_used"],
                "tasks_limit": usage["tasks_limit"],
                "tasks_remaining": usage["tasks_limit"] - usage["tasks_used"],
                "can_execute": usage["tasks_used"] < usage["tasks_limit"]
            }
            
        except Exception as e:
            self.log_error(f"Error checking user quota for {user_id}: {e}")
            return {"can_execute": False, "error": str(e)}
    
    async def consume_user_quota(self, user_id: str, tier: str) -> bool:
        """Consume one browser automation task from user's quota"""
        try:
            current_month = datetime.now().strftime("%Y-%m")
            
            # Update usage count
            result = self.supabase.table("browser_automation_usage").update({
                "tasks_used": "tasks_used + 1",
                "last_task_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }).eq("user_id", user_id).eq("month_year", current_month).execute()
            
            if result.data:
                self.log_info(f"Quota consumed for user {user_id}")
                return True
            return False
            
        except Exception as e:
            self.log_error(f"Error consuming quota for {user_id}: {e}")
            return False
    
    async def log_browser_analytics(
        self,
        user_id: str,
        event_type: str,
        event_data: Dict[str, Any] = None,
        task_id: str = None,
        tier: str = None,
        config_id: str = None,
        execution_time_ms: int = None,
        success: bool = None,
        error_type: str = None
    ):
        """Log browser automation analytics event"""
        try:
            analytics_data = {
                "user_id": user_id,
                "event_type": event_type,
                "event_data": event_data or {},
                "task_id": task_id,
                "tier": tier,
                "config_id": config_id,
                "execution_time_ms": execution_time_ms,
                "success": success,
                "error_type": error_type
            }
            
            self.supabase.table("browser_automation_analytics").insert(analytics_data).execute()
            
        except Exception as e:
            self.log_error(f"Error logging analytics: {e}")
    
    async def get_user_browser_tasks(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0,
        status: str = None
    ) -> List[Dict[str, Any]]:
        """Get user's browser automation tasks"""
        try:
            query = self.supabase.table("browser_automation_tasks").select("*").eq("user_id", user_id)
            
            if status:
                query = query.eq("status", status)
            
            result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
            
            return result.data or []
            
        except Exception as e:
            self.log_error(f"Error getting user tasks for {user_id}: {e}")
            return []


# Global database service instance
database_service = DatabaseService()
