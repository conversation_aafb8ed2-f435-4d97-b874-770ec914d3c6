"""
Memory Integration Service
Handles Browser Use memory functionality with Mem0 and Qdrant integration
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from browser_use.agent.memory import MemoryConfig
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class MemoryIntegration(LoggerMixin):
    """
    Memory integration service for Browser Use agents
    
    Features:
    - Persistent memory across browser sessions
    - Context-aware memory retrieval
    - Task-specific memory isolation
    - Memory summarization and optimization
    - Integration with Qdrant vector store
    """
    
    def __init__(self):
        self.qdrant_client: Optional[QdrantClient] = None
        self.memory_configs: Dict[str, MemoryConfig] = {}
        self.active_memories: Dict[str, List[Dict[str, Any]]] = {}
        
    async def initialize(self):
        """Initialize memory integration service"""
        try:
            # Initialize Qdrant client
            self.qdrant_client = QdrantClient(
                host=settings.QDRANT_HOST,
                port=settings.QDRANT_PORT,
                api_key=settings.QDRANT_API_KEY
            )
            
            # Test connection
            collections = await self.qdrant_client.get_collections()
            self.log_info("Memory integration initialized", collections_count=len(collections.collections))
            
        except Exception as e:
            self.log_error(f"Failed to initialize memory integration: {e}")
            raise BrowserAutomationException(f"Memory initialization failed: {e}")
    
    def create_memory_config(
        self,
        agent_id: str,
        llm_instance: Any,
        user_id: str = None,
        task_id: str = None
    ) -> MemoryConfig:
        """Create memory configuration for a Browser Use agent"""
        try:
            # Create unique memory identifier
            memory_id = f"rokey_{agent_id}_{user_id or 'default'}_{task_id or 'session'}"
            
            # Create memory configuration
            memory_config = MemoryConfig(
                llm_instance=llm_instance,
                agent_id=memory_id,
                memory_interval=10,  # Summarize every 10 steps
                vector_store_provider="qdrant",
                vector_store_config_override={
                    "host": settings.QDRANT_HOST,
                    "port": settings.QDRANT_PORT,
                    "api_key": settings.QDRANT_API_KEY,
                    "collection_name": f"rokey_memory_{agent_id}"
                }
            )
            
            # Store configuration
            self.memory_configs[memory_id] = memory_config
            
            self.log_info(f"Memory config created for agent: {agent_id}")
            
            return memory_config
            
        except Exception as e:
            self.log_error(f"Failed to create memory config: {e}")
            raise BrowserAutomationException(f"Memory config creation failed: {e}")
    
    async def store_memory(
        self,
        agent_id: str,
        memory_type: str,
        content: str,
        metadata: Dict[str, Any] = None,
        importance: float = 0.5
    ) -> str:
        """Store a memory entry"""
        try:
            memory_id = str(uuid.uuid4())
            
            memory_entry = {
                "id": memory_id,
                "agent_id": agent_id,
                "type": memory_type,
                "content": content,
                "metadata": metadata or {},
                "importance": importance,
                "timestamp": datetime.now().isoformat(),
                "access_count": 0,
                "last_accessed": None
            }
            
            # Store in active memories
            if agent_id not in self.active_memories:
                self.active_memories[agent_id] = []
            
            self.active_memories[agent_id].append(memory_entry)
            
            # Store in vector database if available
            if self.qdrant_client:
                await self._store_in_vector_db(agent_id, memory_entry)
            
            self.log_info(f"Memory stored for agent {agent_id}: {memory_type}")
            
            return memory_id
            
        except Exception as e:
            self.log_error(f"Failed to store memory: {e}")
            raise BrowserAutomationException(f"Memory storage failed: {e}")
    
    async def retrieve_memories(
        self,
        agent_id: str,
        query: str = None,
        memory_type: str = None,
        limit: int = 10,
        min_importance: float = 0.0
    ) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for an agent"""
        try:
            memories = []
            
            # Get memories from active storage
            agent_memories = self.active_memories.get(agent_id, [])
            
            # Filter by type if specified
            if memory_type:
                agent_memories = [m for m in agent_memories if m["type"] == memory_type]
            
            # Filter by importance
            agent_memories = [m for m in agent_memories if m["importance"] >= min_importance]
            
            # Sort by importance and recency
            agent_memories.sort(
                key=lambda x: (x["importance"], x["timestamp"]),
                reverse=True
            )
            
            # Take top results
            memories = agent_memories[:limit]
            
            # Update access tracking
            for memory in memories:
                memory["access_count"] += 1
                memory["last_accessed"] = datetime.now().isoformat()
            
            self.log_info(f"Retrieved {len(memories)} memories for agent {agent_id}")
            
            return memories
            
        except Exception as e:
            self.log_error(f"Failed to retrieve memories: {e}")
            return []
    
    async def summarize_session_memory(
        self,
        agent_id: str,
        session_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Summarize memory from a browser session"""
        try:
            agent_memories = self.active_memories.get(agent_id, [])
            
            if not agent_memories:
                return {"summary": "No memories to summarize", "key_points": []}
            
            # Group memories by type
            memory_groups = {}
            for memory in agent_memories:
                mem_type = memory["type"]
                if mem_type not in memory_groups:
                    memory_groups[mem_type] = []
                memory_groups[mem_type].append(memory)
            
            # Create summary
            summary_data = {
                "session_id": session_context.get("session_id"),
                "agent_id": agent_id,
                "total_memories": len(agent_memories),
                "memory_types": list(memory_groups.keys()),
                "session_duration": session_context.get("duration"),
                "key_actions": [],
                "important_findings": [],
                "errors_encountered": [],
                "summary_timestamp": datetime.now().isoformat()
            }
            
            # Extract key information from memories
            for mem_type, memories in memory_groups.items():
                if mem_type == "action":
                    summary_data["key_actions"] = [
                        m["content"] for m in memories 
                        if m["importance"] > 0.7
                    ][:5]
                elif mem_type == "finding":
                    summary_data["important_findings"] = [
                        m["content"] for m in memories 
                        if m["importance"] > 0.6
                    ][:5]
                elif mem_type == "error":
                    summary_data["errors_encountered"] = [
                        m["content"] for m in memories
                    ][:3]
            
            # Store session summary as a high-importance memory
            await self.store_memory(
                agent_id=agent_id,
                memory_type="session_summary",
                content=f"Session summary: {summary_data}",
                metadata=summary_data,
                importance=0.9
            )
            
            self.log_info(f"Session memory summarized for agent {agent_id}")
            
            return summary_data
            
        except Exception as e:
            self.log_error(f"Failed to summarize session memory: {e}")
            return {"error": str(e)}
    
    async def clear_agent_memory(
        self,
        agent_id: str,
        memory_type: str = None,
        older_than_hours: int = None
    ) -> int:
        """Clear memories for an agent"""
        try:
            if agent_id not in self.active_memories:
                return 0
            
            original_count = len(self.active_memories[agent_id])
            
            # Filter memories to keep
            memories_to_keep = []
            
            for memory in self.active_memories[agent_id]:
                should_remove = False
                
                # Filter by type
                if memory_type and memory["type"] == memory_type:
                    should_remove = True
                
                # Filter by age
                if older_than_hours:
                    memory_time = datetime.fromisoformat(memory["timestamp"])
                    cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
                    if memory_time < cutoff_time:
                        should_remove = True
                
                # If no filters specified, remove all
                if not memory_type and not older_than_hours:
                    should_remove = True
                
                if not should_remove:
                    memories_to_keep.append(memory)
            
            # Update active memories
            self.active_memories[agent_id] = memories_to_keep
            
            cleared_count = original_count - len(memories_to_keep)
            
            self.log_info(f"Cleared {cleared_count} memories for agent {agent_id}")
            
            return cleared_count
            
        except Exception as e:
            self.log_error(f"Failed to clear agent memory: {e}")
            return 0
    
    async def get_memory_stats(self, agent_id: str = None) -> Dict[str, Any]:
        """Get memory statistics"""
        try:
            if agent_id:
                # Stats for specific agent
                agent_memories = self.active_memories.get(agent_id, [])
                
                memory_types = {}
                total_importance = 0
                
                for memory in agent_memories:
                    mem_type = memory["type"]
                    memory_types[mem_type] = memory_types.get(mem_type, 0) + 1
                    total_importance += memory["importance"]
                
                return {
                    "agent_id": agent_id,
                    "total_memories": len(agent_memories),
                    "memory_types": memory_types,
                    "average_importance": total_importance / len(agent_memories) if agent_memories else 0,
                    "oldest_memory": min(m["timestamp"] for m in agent_memories) if agent_memories else None,
                    "newest_memory": max(m["timestamp"] for m in agent_memories) if agent_memories else None
                }
            else:
                # Global stats
                total_memories = sum(len(memories) for memories in self.active_memories.values())
                active_agents = len(self.active_memories)
                
                return {
                    "total_memories": total_memories,
                    "active_agents": active_agents,
                    "memory_configs": len(self.memory_configs),
                    "average_memories_per_agent": total_memories / active_agents if active_agents else 0
                }
                
        except Exception as e:
            self.log_error(f"Failed to get memory stats: {e}")
            return {"error": str(e)}
    
    async def _store_in_vector_db(self, agent_id: str, memory_entry: Dict[str, Any]):
        """Store memory entry in vector database"""
        try:
            collection_name = f"rokey_memory_{agent_id}"
            
            # Ensure collection exists
            try:
                await self.qdrant_client.get_collection(collection_name)
            except Exception:
                # Create collection if it doesn't exist
                await self.qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(size=384, distance=Distance.COSINE)
                )
            
            # For now, store without embedding (would need embedding model)
            # In production, you'd generate embeddings for the content
            point = PointStruct(
                id=memory_entry["id"],
                vector=[0.0] * 384,  # Placeholder vector
                payload={
                    "content": memory_entry["content"],
                    "type": memory_entry["type"],
                    "metadata": memory_entry["metadata"],
                    "importance": memory_entry["importance"],
                    "timestamp": memory_entry["timestamp"]
                }
            )
            
            await self.qdrant_client.upsert(
                collection_name=collection_name,
                points=[point]
            )
            
        except Exception as e:
            self.log_warning(f"Failed to store in vector DB: {e}")
            # Don't raise exception - memory still stored in active storage
    
    async def cleanup(self):
        """Cleanup memory integration resources"""
        try:
            if self.qdrant_client:
                await self.qdrant_client.close()
            
            self.active_memories.clear()
            self.memory_configs.clear()
            
            self.log_info("Memory integration cleanup completed")
            
        except Exception as e:
            self.log_error(f"Memory cleanup failed: {e}")


# Global memory integration instance
memory_integration = MemoryIntegration()
