#!/usr/bin/env python3
"""
Comprehensive End-to-End Testing for Milestone 2: Browser Automation
Tests the complete workflow with real Pro user data and configurations
"""

import asyncio
import httpx
import json
import time
from typing import Dict, List, Any
from datetime import datetime

# Real Pro User Data from Supabase
REAL_USER_DATA = {
    "user_id": "69d967d5-0b7b-402b-ae1b-711d9b74eef4",
    "email": "<EMAIL>",
    "tier": "professional",
    "config_id": "b39270c0-feb8-4c99-b6d2-9ee224edd57e",
    "config_name": "openrouter",
    "routing_strategy": "intelligent_role",
    "browser_automation_enabled": False  # We'll test enabling this
}

# Real API Keys from User's Configuration
REAL_API_KEYS = [
    {
        "id": "********-9efb-4a88-8eba-490625742d16",
        "provider": "openrouter",
        "model": "microsoft/phi-4-reasoning:free",
        "label": "llama"
    },
    {
        "id": "7f4583a5-6f78-4d3c-99a7-bf8c54ce49e5",
        "provider": "google",
        "model": "google/gemini-2.0-flash-001",
        "label": "gem flash 2"
    },
    {
        "id": "e3dcda06-0be2-4f0a-b5e2-0c6a4484a638",
        "provider": "openrouter",
        "model": "arliai/qwq-32b-arliai-rpr-v1:free",
        "label": "gem"
    },
    {
        "id": "********-9e50-4afc-a903-babcb9066993",
        "provider": "google",
        "model": "google/gemini-2.5-flash-preview-05-20",
        "label": "gem flash"
    }
]

# Comprehensive Test Cases
TEST_CASES = [
    {
        "name": "Messi Football Information",
        "task": "when does messi play his next match and did he score in his last?",
        "expected_actions": ["search", "navigate", "extract"],
        "timeout": 300,
        "complexity": "high"
    },
    {
        "name": "Weather Information",
        "task": "what's the weather like in New York today?",
        "expected_actions": ["search", "navigate", "extract"],
        "timeout": 180,
        "complexity": "medium"
    },
    {
        "name": "Stock Market Data",
        "task": "what is the current price of Apple stock and how did it perform this week?",
        "expected_actions": ["search", "navigate", "extract", "analyze"],
        "timeout": 240,
        "complexity": "high"
    },
    {
        "name": "News Headlines",
        "task": "get me the top 3 technology news headlines from today",
        "expected_actions": ["search", "navigate", "extract"],
        "timeout": 200,
        "complexity": "medium"
    },
    {
        "name": "Product Information",
        "task": "find the price and specifications of the latest iPhone on Apple's website",
        "expected_actions": ["navigate", "search", "extract"],
        "timeout": 250,
        "complexity": "medium"
    }
]

class Milestone2E2ETester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=600.0)
        self.test_results = []
        
    async def run_comprehensive_tests(self):
        """Run all comprehensive tests for Milestone 2"""
        print("🚀 Starting Milestone 2 End-to-End Testing")
        print("=" * 60)
        print(f"Testing with real Pro user: {REAL_USER_DATA['email']}")
        print(f"User ID: {REAL_USER_DATA['user_id']}")
        print(f"Config: {REAL_USER_DATA['config_name']} ({REAL_USER_DATA['routing_strategy']})")
        print(f"API Keys: {len(REAL_API_KEYS)} configured")
        print("=" * 60)
        
        # Test service health first
        await self._test_service_health()
        
        # Test each comprehensive test case
        for i, test_case in enumerate(TEST_CASES, 1):
            print(f"\n🧪 Test {i}/{len(TEST_CASES)}: {test_case['name']}")
            print("-" * 40)
            await self._run_single_test(test_case)
            
        # Generate comprehensive report
        await self._generate_final_report()
        
    async def _test_service_health(self):
        """Test that the browser automation service is healthy"""
        print("\n🏥 Testing Service Health...")
        
        try:
            response = await self.client.get(f"{self.base_url}/health/ready")
            if response.status_code == 200:
                print("✅ Service is healthy and ready")
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
                
            # Test detailed health
            response = await self.client.get(f"{self.base_url}/api/v1/test/health-detailed")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Detailed health check passed")
                print(f"   - Browser Use: {health_data.get('browser_use_available', 'Unknown')}")
                print(f"   - LangGraph: {health_data.get('langgraph_available', 'Unknown')}")
            else:
                print(f"⚠️ Detailed health check failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Service health check error: {e}")
            return False
            
        return True
        
    async def _run_single_test(self, test_case: Dict[str, Any]):
        """Run a single comprehensive test case"""
        start_time = time.time()
        
        # Prepare request with real user data
        request_data = {
            "user_id": REAL_USER_DATA["user_id"],
            "config_id": REAL_USER_DATA["config_id"],
            "user_tier": REAL_USER_DATA["tier"],
            "task": test_case["task"],
            "workflow_type": "hierarchical",  # Use hierarchical for complex tasks
            "routing_strategy": REAL_USER_DATA["routing_strategy"],
            "max_roles": 3,
            "max_steps": 10,
            "timeout_seconds": test_case["timeout"],
            "enable_memory": True,
            "enable_screenshots": True,
            "headless": True,
            "verify_results": True,
            "api_keys": REAL_API_KEYS
        }
        
        print(f"📝 Task: {test_case['task']}")
        print(f"⏱️ Timeout: {test_case['timeout']}s")
        print(f"🎯 Expected complexity: {test_case['complexity']}")
        
        try:
            # Execute the browser automation task
            print("🔄 Executing browser automation...")
            response = await self.client.post(
                f"{self.base_url}/api/v1/browser/execute",
                json=request_data
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                await self._analyze_test_result(test_case, result, execution_time)
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                self._record_failed_test(test_case, execution_time, f"HTTP {response.status_code}")
                
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ Test execution failed: {e}")
            self._record_failed_test(test_case, execution_time, str(e))
            
    async def _analyze_test_result(self, test_case: Dict[str, Any], result: Dict[str, Any], execution_time: float):
        """Analyze and report on test results"""
        success = result.get("success", False)
        task_id = result.get("task_id", "unknown")
        
        print(f"📊 Task ID: {task_id}")
        print(f"✅ Success: {success}")
        print(f"⏱️ Execution time: {execution_time:.2f}s")
        
        # Analyze execution metadata
        metadata = result.get("execution_metadata", {})
        steps_completed = metadata.get("steps_completed", 0)
        roles_used = metadata.get("roles_used", [])
        pages_visited = metadata.get("pages_visited", [])
        
        print(f"🔢 Steps completed: {steps_completed}")
        print(f"👥 Roles used: {', '.join(roles_used) if roles_used else 'None'}")
        print(f"🌐 Pages visited: {len(pages_visited)}")
        
        # Analyze results
        final_result = result.get("result", "")
        print(f"📋 Result preview: {final_result[:200]}..." if len(final_result) > 200 else f"📋 Result: {final_result}")
        
        # Check if expected actions were performed
        expected_actions = test_case.get("expected_actions", [])
        actions_performed = metadata.get("actions_performed", [])
        
        print(f"🎯 Expected actions: {', '.join(expected_actions)}")
        print(f"✅ Actions performed: {', '.join(actions_performed) if actions_performed else 'None detected'}")
        
        # Record comprehensive test result
        test_result = {
            "test_name": test_case["name"],
            "task": test_case["task"],
            "success": success,
            "execution_time": execution_time,
            "task_id": task_id,
            "steps_completed": steps_completed,
            "roles_used": roles_used,
            "pages_visited": len(pages_visited),
            "actions_performed": actions_performed,
            "expected_actions": expected_actions,
            "result_length": len(final_result),
            "complexity": test_case["complexity"],
            "timestamp": datetime.now().isoformat()
        }
        
        self.test_results.append(test_result)
        
        if success:
            print("🎉 Test PASSED!")
        else:
            print("❌ Test FAILED!")
            
    def _record_failed_test(self, test_case: Dict[str, Any], execution_time: float, error: str):
        """Record a failed test"""
        test_result = {
            "test_name": test_case["name"],
            "task": test_case["task"],
            "success": False,
            "execution_time": execution_time,
            "error": error,
            "complexity": test_case["complexity"],
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(test_result)
        
    async def _generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n" + "=" * 60)
        print("📊 MILESTONE 2 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📈 Overall Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"   Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        
        if self.test_results:
            avg_execution_time = sum(r["execution_time"] for r in self.test_results) / len(self.test_results)
            print(f"   Average execution time: {avg_execution_time:.2f}s")
            
        print(f"\n📋 Detailed Results:")
        for i, result in enumerate(self.test_results, 1):
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"   {i}. {result['test_name']}: {status} ({result['execution_time']:.1f}s)")
            if not result["success"] and "error" in result:
                print(f"      Error: {result['error']}")
                
        # Save detailed report
        report_file = f"milestone_2_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "test_summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": passed_tests/total_tests*100 if total_tests > 0 else 0,
                    "average_execution_time": avg_execution_time if self.test_results else 0
                },
                "user_data": REAL_USER_DATA,
                "api_keys_count": len(REAL_API_KEYS),
                "detailed_results": self.test_results
            }, f, indent=2)
            
        print(f"\n💾 Detailed report saved to: {report_file}")
        print("=" * 60)
        
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main test execution"""
    tester = Milestone2E2ETester()
    
    try:
        await tester.run_comprehensive_tests()
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
