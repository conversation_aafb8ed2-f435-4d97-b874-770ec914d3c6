"""
Task Planner Agent
Generates detailed todo lists and task decomposition using role-assigned LLMs
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON>hropic

from app.models.browser_automation import TodoItem, TaskStatus
from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class TaskPlanner(LoggerMixin):
    """
    Dynamic task planner that creates detailed todo lists for browser automation
    
    This planner:
    1. Analyzes the main task and breaks it down into actionable subtasks
    2. Estimates complexity and assigns priorities
    3. Creates a structured todo list with dependencies
    4. Uses the user's configured LLM for planning
    """
    
    def __init__(self, llm_config: Optional[Dict[str, Any]] = None):
        self.llm_config = llm_config
        self.llm = self._create_llm() if llm_config else None
    
    def _create_llm(self):
        """Create LLM instance for planning"""
        if not self.llm_config:
            # Fallback to default OpenAI
            return ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                model="gpt-4o",
                temperature=0.3,  # Lower temperature for structured planning
                max_tokens=2000
            )
        
        provider = self.llm_config.get("provider", "openai").lower()
        api_key = self.llm_config.get("api_key")
        model = self.llm_config.get("model", "gpt-4o")
        
        if provider == "openai":
            return ChatOpenAI(
                api_key=api_key,
                model=model,
                temperature=0.3,
                max_tokens=2000
            )
        elif provider == "anthropic":
            return ChatAnthropic(
                api_key=api_key,
                model=model,
                temperature=0.3,
                max_tokens=2000
            )
        else:
            # Fallback to OpenAI
            return ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=2000
            )
    
    async def create_task_plan(
        self,
        main_task: str,
        available_roles: List[str] = None,
        user_tier: str = "pro",
        max_steps: int = 50
    ) -> Dict[str, Any]:
        """
        Create a comprehensive task plan with todo list
        
        Args:
            main_task: The main task to be accomplished
            available_roles: List of available roles for task assignment
            user_tier: User's subscription tier (affects complexity)
            max_steps: Maximum number of steps allowed
            
        Returns:
            Dictionary containing todo list, complexity analysis, and metadata
        """
        try:
            self.log_info("Creating task plan", task=main_task[:100])
            
            # Create planning prompt
            planning_prompt = self._create_planning_prompt(
                main_task, 
                available_roles or ["general_chat"], 
                user_tier, 
                max_steps
            )
            
            # Get LLM planning response
            if not self.llm:
                self.llm = self._create_llm()
            
            response = await self.llm.ainvoke([
                SystemMessage(content=self._get_planning_system_prompt()),
                HumanMessage(content=planning_prompt)
            ])
            
            # Parse and structure the response
            plan = await self._parse_planning_response(response.content, main_task)
            
            self.log_info(
                "Task plan created",
                todo_items=len(plan.get("todo_list", [])),
                complexity=plan.get("complexity", "unknown")
            )
            
            return plan
            
        except Exception as e:
            self.log_error(f"Task planning failed: {e}")
            # Return fallback plan
            return await self._create_fallback_plan(main_task, available_roles)
    
    def _get_planning_system_prompt(self) -> str:
        """Get system prompt for task planning"""
        return """You are an expert task planner for browser automation. Your job is to break down complex tasks into actionable subtasks.

IMPORTANT: Respond with a structured JSON format containing:
1. "complexity": "simple" | "medium" | "complex"
2. "estimated_time_minutes": number
3. "required_agents": list of agent types needed
4. "verification_needed": boolean
5. "todo_list": array of subtasks

Each subtask should have:
- "id": unique identifier
- "task": clear, actionable description
- "priority": 1-10 (higher = more important)
- "estimated_steps": number of browser actions needed
- "assigned_agent": which type of agent should handle this
- "dependencies": list of task IDs this depends on

Focus on:
- Clear, actionable subtasks
- Logical sequence and dependencies
- Realistic step estimates
- Appropriate agent assignment

Example response format:
{
  "complexity": "medium",
  "estimated_time_minutes": 5,
  "required_agents": ["web_research", "general_chat"],
  "verification_needed": true,
  "todo_list": [
    {
      "id": "task_1",
      "task": "Navigate to Amazon.com",
      "priority": 10,
      "estimated_steps": 2,
      "assigned_agent": "general_chat",
      "dependencies": []
    }
  ]
}"""
    
    def _create_planning_prompt(
        self,
        main_task: str,
        available_roles: List[str],
        user_tier: str,
        max_steps: int
    ) -> str:
        """Create the planning prompt for the LLM"""
        
        tier_constraints = {
            "free": "No browser automation available",
            "starter": f"Maximum {max_steps} steps, basic websites only",
            "pro": f"Maximum {max_steps} steps, all websites",
            "enterprise": f"Maximum {max_steps} steps, complex workflows allowed"
        }
        
        return f"""
MAIN TASK: {main_task}

AVAILABLE AGENTS: {', '.join(available_roles)}

USER TIER: {user_tier}
CONSTRAINTS: {tier_constraints.get(user_tier, 'Standard constraints')}

Please create a detailed task plan that breaks down this main task into specific, actionable subtasks. Consider:

1. What websites need to be visited?
2. What information needs to be extracted?
3. What actions need to be performed (clicking, typing, scrolling)?
4. Should results be verified with additional searches?
5. How should the final response be formatted?

Make sure each subtask is:
- Specific and actionable
- Assigned to the most appropriate agent
- Properly sequenced with dependencies
- Realistic in scope (not too broad or too narrow)

Respond with the JSON format specified in the system prompt.
"""
    
    async def _parse_planning_response(
        self,
        response_content: str,
        main_task: str
    ) -> Dict[str, Any]:
        """Parse the LLM planning response into structured format"""
        try:
            import json
            
            # Try to extract JSON from response
            response_content = response_content.strip()
            if response_content.startswith("```json"):
                response_content = response_content[7:]
            if response_content.endswith("```"):
                response_content = response_content[:-3]
            
            # Parse JSON
            parsed_plan = json.loads(response_content)
            
            # Convert to TodoItem objects
            todo_list = []
            for item in parsed_plan.get("todo_list", []):
                todo_item = TodoItem(
                    id=item.get("id", str(uuid.uuid4())),
                    task=item.get("task", ""),
                    priority=item.get("priority", 1),
                    assigned_agent=item.get("assigned_agent", "general_chat"),
                    dependencies=item.get("dependencies", []),
                    status=TaskStatus.PENDING
                )
                todo_list.append(todo_item)
            
            # Structure the complete plan
            plan = {
                "main_task": main_task,
                "complexity": parsed_plan.get("complexity", "medium"),
                "estimated_time_minutes": parsed_plan.get("estimated_time_minutes", 10),
                "required_agents": parsed_plan.get("required_agents", ["general_chat"]),
                "verification_needed": parsed_plan.get("verification_needed", True),
                "todo_list": [item.dict() for item in todo_list],
                "total_tasks": len(todo_list),
                "created_at": datetime.now().isoformat()
            }
            
            return plan
            
        except json.JSONDecodeError as e:
            self.log_error(f"Failed to parse planning response as JSON: {e}")
            return await self._create_fallback_plan(main_task, ["general_chat"])
        except Exception as e:
            self.log_error(f"Error parsing planning response: {e}")
            return await self._create_fallback_plan(main_task, ["general_chat"])
    
    async def _create_fallback_plan(
        self,
        main_task: str,
        available_roles: List[str] = None
    ) -> Dict[str, Any]:
        """Create a simple fallback plan when planning fails"""
        
        available_roles = available_roles or ["general_chat"]
        primary_agent = available_roles[0]
        
        # Create basic todo list
        todo_list = [
            TodoItem(
                id="fallback_1",
                task=f"Analyze the main task: {main_task}",
                priority=10,
                assigned_agent=primary_agent,
                dependencies=[],
                status=TaskStatus.PENDING
            ),
            TodoItem(
                id="fallback_2",
                task="Navigate to relevant website",
                priority=9,
                assigned_agent=primary_agent,
                dependencies=["fallback_1"],
                status=TaskStatus.PENDING
            ),
            TodoItem(
                id="fallback_3",
                task="Extract required information",
                priority=8,
                assigned_agent=primary_agent,
                dependencies=["fallback_2"],
                status=TaskStatus.PENDING
            ),
            TodoItem(
                id="fallback_4",
                task="Format and return results",
                priority=7,
                assigned_agent=primary_agent,
                dependencies=["fallback_3"],
                status=TaskStatus.PENDING
            )
        ]
        
        return {
            "main_task": main_task,
            "complexity": "medium",
            "estimated_time_minutes": 5,
            "required_agents": available_roles,
            "verification_needed": False,
            "todo_list": [item.dict() for item in todo_list],
            "total_tasks": len(todo_list),
            "created_at": datetime.now().isoformat(),
            "fallback_plan": True
        }
    
    async def update_plan_progress(
        self,
        plan: Dict[str, Any],
        completed_task_id: str,
        result: str
    ) -> Dict[str, Any]:
        """Update plan progress when a task is completed"""
        try:
            updated_plan = plan.copy()
            
            # Update the completed task
            for todo_item in updated_plan["todo_list"]:
                if todo_item["id"] == completed_task_id:
                    todo_item["status"] = TaskStatus.COMPLETED.value
                    todo_item["completion_time"] = datetime.now().isoformat()
                    todo_item["result"] = result
                    break
            
            # Check for newly available tasks (dependencies resolved)
            for todo_item in updated_plan["todo_list"]:
                if todo_item["status"] == TaskStatus.PENDING.value:
                    dependencies = todo_item.get("dependencies", [])
                    if dependencies:
                        # Check if all dependencies are completed
                        all_deps_completed = all(
                            any(
                                t["id"] == dep_id and t["status"] == TaskStatus.COMPLETED.value
                                for t in updated_plan["todo_list"]
                            )
                            for dep_id in dependencies
                        )
                        if all_deps_completed:
                            todo_item["status"] = TaskStatus.PENDING.value  # Ready to start
            
            # Update plan metadata
            completed_count = sum(
                1 for item in updated_plan["todo_list"] 
                if item["status"] == TaskStatus.COMPLETED.value
            )
            updated_plan["progress_percentage"] = (completed_count / len(updated_plan["todo_list"])) * 100
            updated_plan["last_updated"] = datetime.now().isoformat()
            
            self.log_info(
                "Plan progress updated",
                completed_task=completed_task_id,
                progress=f"{completed_count}/{len(updated_plan['todo_list'])}"
            )
            
            return updated_plan
            
        except Exception as e:
            self.log_error(f"Failed to update plan progress: {e}")
            return plan  # Return original plan if update fails
    
    def get_next_available_task(self, plan: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get the next available task that can be executed"""
        try:
            for todo_item in plan["todo_list"]:
                if todo_item["status"] == TaskStatus.PENDING.value:
                    dependencies = todo_item.get("dependencies", [])
                    
                    if not dependencies:
                        # No dependencies, can start immediately
                        return todo_item
                    
                    # Check if all dependencies are completed
                    all_deps_completed = all(
                        any(
                            t["id"] == dep_id and t["status"] == TaskStatus.COMPLETED.value
                            for t in plan["todo_list"]
                        )
                        for dep_id in dependencies
                    )
                    
                    if all_deps_completed:
                        return todo_item
            
            return None  # No available tasks
            
        except Exception as e:
            self.log_error(f"Error getting next available task: {e}")
            return None
