"""
Performance Optimizer
Advanced performance optimization and resource management for browser automation
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
import psutil
import statistics

from app.core.logging import LoggerMixin


class OptimizationStrategy(Enum):
    """Performance optimization strategies"""
    MEMORY_OPTIMIZATION = "memory_optimization"
    CPU_OPTIMIZATION = "cpu_optimization"
    NETWORK_OPTIMIZATION = "network_optimization"
    CONCURRENCY_OPTIMIZATION = "concurrency_optimization"
    CACHE_OPTIMIZATION = "cache_optimization"
    RESOURCE_BALANCING = "resource_balancing"


class PerformanceMetric(Enum):
    """Performance metrics to track"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_UTILIZATION = "resource_utilization"
    QUEUE_LENGTH = "queue_length"
    SESSION_EFFICIENCY = "session_efficiency"


@dataclass
class PerformanceThreshold:
    """Performance threshold configuration"""
    metric: PerformanceMetric
    warning_threshold: float
    critical_threshold: float
    optimization_strategy: OptimizationStrategy
    auto_optimize: bool = True


@dataclass
class OptimizationResult:
    """Result of optimization operation"""
    strategy: OptimizationStrategy
    success: bool
    improvement_percentage: float
    metrics_before: Dict[str, float]
    metrics_after: Dict[str, float]
    execution_time: float
    recommendations: List[str] = field(default_factory=list)


class PerformanceOptimizer(LoggerMixin):
    """
    Advanced performance optimization system
    
    Features:
    - Real-time performance monitoring
    - Automatic optimization triggers
    - Resource usage optimization
    - Predictive scaling
    - Performance analytics
    - Bottleneck detection
    - Load balancing optimization
    - Memory and CPU management
    """
    
    def __init__(self):
        # Performance thresholds
        self.thresholds = {
            PerformanceMetric.RESPONSE_TIME: PerformanceThreshold(
                metric=PerformanceMetric.RESPONSE_TIME,
                warning_threshold=5.0,  # 5 seconds
                critical_threshold=10.0,  # 10 seconds
                optimization_strategy=OptimizationStrategy.CONCURRENCY_OPTIMIZATION
            ),
            PerformanceMetric.THROUGHPUT: PerformanceThreshold(
                metric=PerformanceMetric.THROUGHPUT,
                warning_threshold=10.0,  # 10 tasks/minute
                critical_threshold=5.0,   # 5 tasks/minute
                optimization_strategy=OptimizationStrategy.RESOURCE_BALANCING
            ),
            PerformanceMetric.ERROR_RATE: PerformanceThreshold(
                metric=PerformanceMetric.ERROR_RATE,
                warning_threshold=0.05,  # 5%
                critical_threshold=0.10,  # 10%
                optimization_strategy=OptimizationStrategy.MEMORY_OPTIMIZATION
            ),
            PerformanceMetric.RESOURCE_UTILIZATION: PerformanceThreshold(
                metric=PerformanceMetric.RESOURCE_UTILIZATION,
                warning_threshold=0.80,  # 80%
                critical_threshold=0.90,  # 90%
                optimization_strategy=OptimizationStrategy.CPU_OPTIMIZATION
            )
        }
        
        # Performance metrics history
        self.metrics_history: Dict[PerformanceMetric, List[Tuple[datetime, float]]] = {
            metric: [] for metric in PerformanceMetric
        }
        
        # Optimization history
        self.optimization_history: List[OptimizationResult] = []
        
        # Current performance state
        self.current_metrics: Dict[PerformanceMetric, float] = {}
        
        # Optimization settings
        self.optimization_config = {
            "auto_optimization_enabled": True,
            "optimization_interval": 60.0,  # 1 minute
            "metrics_retention_hours": 24,
            "min_optimization_interval": 300.0,  # 5 minutes between optimizations
            "performance_target_percentile": 95,
            "resource_utilization_target": 0.75
        }
        
        # Background tasks
        self._monitoring_task = None
        self._optimization_task = None
        
        self.log_info("Performance optimizer initialized")
    
    async def initialize(self):
        """Initialize performance optimizer"""
        try:
            self.log_info("Initializing performance optimizer")
            
            # Start background monitoring
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            # Start optimization loop
            if self.optimization_config["auto_optimization_enabled"]:
                self._optimization_task = asyncio.create_task(self._optimization_loop())
            
            self.log_info("Performance optimizer initialized successfully")
            
        except Exception as e:
            self.log_error(f"Performance optimizer initialization failed: {e}")
            raise
    
    async def record_metric(self, metric: PerformanceMetric, value: float):
        """Record a performance metric"""
        try:
            timestamp = datetime.now()
            
            # Add to history
            self.metrics_history[metric].append((timestamp, value))
            
            # Update current metrics
            self.current_metrics[metric] = value
            
            # Cleanup old metrics
            await self._cleanup_old_metrics()
            
            # Check thresholds
            await self._check_thresholds(metric, value)
            
        except Exception as e:
            self.log_error(f"Failed to record metric: {e}")
    
    async def optimize_performance(
        self,
        strategy: OptimizationStrategy,
        target_metrics: Optional[List[PerformanceMetric]] = None
    ) -> OptimizationResult:
        """
        Execute performance optimization
        
        Args:
            strategy: Optimization strategy to apply
            target_metrics: Optional specific metrics to optimize
            
        Returns:
            Optimization result
        """
        try:
            start_time = datetime.now()
            
            self.log_info(f"Starting performance optimization: {strategy.value}")
            
            # Capture metrics before optimization
            metrics_before = await self._capture_current_metrics()
            
            # Execute optimization strategy
            success = await self._execute_optimization_strategy(strategy, target_metrics)
            
            # Wait for metrics to stabilize
            await asyncio.sleep(5.0)
            
            # Capture metrics after optimization
            metrics_after = await self._capture_current_metrics()
            
            # Calculate improvement
            improvement = await self._calculate_improvement(metrics_before, metrics_after)
            
            # Generate recommendations
            recommendations = await self._generate_optimization_recommendations(
                strategy, metrics_before, metrics_after
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Create optimization result
            result = OptimizationResult(
                strategy=strategy,
                success=success,
                improvement_percentage=improvement,
                metrics_before=metrics_before,
                metrics_after=metrics_after,
                execution_time=execution_time,
                recommendations=recommendations
            )
            
            # Record optimization
            self.optimization_history.append(result)
            
            self.log_info(
                f"Optimization completed: {strategy.value}",
                success=success,
                improvement=f"{improvement:.1f}%",
                execution_time=execution_time
            )
            
            return result
            
        except Exception as e:
            self.log_error(f"Performance optimization failed: {e}")
            return OptimizationResult(
                strategy=strategy,
                success=False,
                improvement_percentage=0.0,
                metrics_before={},
                metrics_after={},
                execution_time=0.0,
                recommendations=[f"Optimization failed: {e}"]
            )
    
    async def get_performance_report(self, time_period: str = "1h") -> Dict[str, Any]:
        """
        Generate comprehensive performance report
        
        Args:
            time_period: Time period for analysis (1h, 6h, 24h)
            
        Returns:
            Performance report
        """
        try:
            # Parse time period
            if time_period == "1h":
                cutoff = datetime.now() - timedelta(hours=1)
            elif time_period == "6h":
                cutoff = datetime.now() - timedelta(hours=6)
            elif time_period == "24h":
                cutoff = datetime.now() - timedelta(hours=24)
            else:
                cutoff = datetime.now() - timedelta(hours=1)
            
            # Analyze metrics
            metrics_analysis = await self._analyze_metrics(cutoff)
            
            # Analyze optimizations
            optimization_analysis = await self._analyze_optimizations(cutoff)
            
            # Detect bottlenecks
            bottlenecks = await self._detect_bottlenecks(cutoff)
            
            # Generate recommendations
            recommendations = await self._generate_performance_recommendations()
            
            # Get current resource usage
            resource_usage = await self._get_resource_usage()
            
            return {
                "time_period": time_period,
                "generated_at": datetime.now().isoformat(),
                "current_metrics": self.current_metrics,
                "metrics_analysis": metrics_analysis,
                "optimization_analysis": optimization_analysis,
                "bottlenecks": bottlenecks,
                "recommendations": recommendations,
                "resource_usage": resource_usage,
                "performance_score": await self._calculate_performance_score()
            }
            
        except Exception as e:
            self.log_error(f"Performance report generation failed: {e}")
            return {"error": str(e)}
    
    async def predict_performance_issues(self) -> List[Dict[str, Any]]:
        """
        Predict potential performance issues using trend analysis
        
        Returns:
            List of predicted issues
        """
        try:
            predictions = []
            
            for metric, history in self.metrics_history.items():
                if len(history) < 10:  # Need sufficient data
                    continue
                
                # Analyze trend
                recent_values = [value for _, value in history[-10:]]
                trend = await self._calculate_trend(recent_values)
                
                # Predict future value
                predicted_value = recent_values[-1] + (trend * 5)  # 5 periods ahead
                
                # Check if prediction exceeds thresholds
                threshold = self.thresholds.get(metric)
                if threshold:
                    if predicted_value > threshold.critical_threshold:
                        predictions.append({
                            "metric": metric.value,
                            "current_value": recent_values[-1],
                            "predicted_value": predicted_value,
                            "threshold": threshold.critical_threshold,
                            "severity": "critical",
                            "estimated_time": "5-10 minutes",
                            "recommended_action": threshold.optimization_strategy.value
                        })
                    elif predicted_value > threshold.warning_threshold:
                        predictions.append({
                            "metric": metric.value,
                            "current_value": recent_values[-1],
                            "predicted_value": predicted_value,
                            "threshold": threshold.warning_threshold,
                            "severity": "warning",
                            "estimated_time": "10-15 minutes",
                            "recommended_action": "monitor_closely"
                        })
            
            return predictions
            
        except Exception as e:
            self.log_error(f"Performance prediction failed: {e}")
            return []
    
    async def shutdown(self):
        """Shutdown performance optimizer"""
        try:
            self.log_info("Shutting down performance optimizer")
            
            # Cancel background tasks
            if self._monitoring_task:
                self._monitoring_task.cancel()
            if self._optimization_task:
                self._optimization_task.cancel()
            
            self.log_info("Performance optimizer shutdown completed")
            
        except Exception as e:
            self.log_error(f"Performance optimizer shutdown failed: {e}")
    
    # Helper methods
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                await self._collect_system_metrics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Monitoring loop error: {e}")
    
    async def _optimization_loop(self):
        """Background optimization loop"""
        while True:
            try:
                await asyncio.sleep(self.optimization_config["optimization_interval"])
                await self._auto_optimize()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Optimization loop error: {e}")
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            process = psutil.Process()
            
            # CPU utilization
            cpu_percent = process.cpu_percent()
            await self.record_metric(PerformanceMetric.RESOURCE_UTILIZATION, cpu_percent / 100.0)
            
            # Memory utilization
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # Calculate additional metrics based on system state
            # This would integrate with session manager and other components
            
        except Exception as e:
            self.log_error(f"System metrics collection failed: {e}")
    
    async def _check_thresholds(self, metric: PerformanceMetric, value: float):
        """Check if metric exceeds thresholds"""
        try:
            threshold = self.thresholds.get(metric)
            if not threshold:
                return
            
            if value > threshold.critical_threshold:
                self.log_warning(
                    f"Critical threshold exceeded: {metric.value}",
                    value=value,
                    threshold=threshold.critical_threshold
                )
                
                if threshold.auto_optimize:
                    await self.optimize_performance(threshold.optimization_strategy, [metric])
                    
            elif value > threshold.warning_threshold:
                self.log_info(
                    f"Warning threshold exceeded: {metric.value}",
                    value=value,
                    threshold=threshold.warning_threshold
                )
                
        except Exception as e:
            self.log_error(f"Threshold check failed: {e}")
    
    async def _cleanup_old_metrics(self):
        """Clean up old metrics data"""
        try:
            cutoff = datetime.now() - timedelta(hours=self.optimization_config["metrics_retention_hours"])
            
            for metric, history in self.metrics_history.items():
                # Remove old entries
                self.metrics_history[metric] = [
                    (timestamp, value) for timestamp, value in history
                    if timestamp > cutoff
                ]
                
        except Exception as e:
            self.log_error(f"Metrics cleanup failed: {e}")
    
    async def _execute_optimization_strategy(
        self,
        strategy: OptimizationStrategy,
        target_metrics: Optional[List[PerformanceMetric]]
    ) -> bool:
        """Execute specific optimization strategy"""
        try:
            if strategy == OptimizationStrategy.MEMORY_OPTIMIZATION:
                return await self._optimize_memory()
            elif strategy == OptimizationStrategy.CPU_OPTIMIZATION:
                return await self._optimize_cpu()
            elif strategy == OptimizationStrategy.CONCURRENCY_OPTIMIZATION:
                return await self._optimize_concurrency()
            elif strategy == OptimizationStrategy.RESOURCE_BALANCING:
                return await self._optimize_resource_balancing()
            else:
                self.log_warning(f"Unknown optimization strategy: {strategy.value}")
                return False
                
        except Exception as e:
            self.log_error(f"Optimization strategy execution failed: {e}")
            return False
    
    async def _optimize_memory(self) -> bool:
        """Optimize memory usage"""
        try:
            # Trigger garbage collection
            import gc
            gc.collect()
            
            # Additional memory optimization would go here
            # e.g., clearing caches, optimizing session pool
            
            return True
            
        except Exception as e:
            self.log_error(f"Memory optimization failed: {e}")
            return False
    
    async def _optimize_cpu(self) -> bool:
        """Optimize CPU usage"""
        try:
            # CPU optimization strategies would go here
            # e.g., adjusting concurrency limits, optimizing algorithms
            
            return True
            
        except Exception as e:
            self.log_error(f"CPU optimization failed: {e}")
            return False
    
    async def _optimize_concurrency(self) -> bool:
        """Optimize concurrency settings"""
        try:
            # Concurrency optimization would go here
            # e.g., adjusting session pool size, task queue limits
            
            return True
            
        except Exception as e:
            self.log_error(f"Concurrency optimization failed: {e}")
            return False
    
    async def _optimize_resource_balancing(self) -> bool:
        """Optimize resource balancing"""
        try:
            # Resource balancing optimization would go here
            # e.g., load balancing, resource allocation
            
            return True
            
        except Exception as e:
            self.log_error(f"Resource balancing optimization failed: {e}")
            return False
    
    async def _capture_current_metrics(self) -> Dict[str, float]:
        """Capture current performance metrics"""
        try:
            return {
                metric.value: value
                for metric, value in self.current_metrics.items()
            }
        except Exception:
            return {}
    
    async def _calculate_improvement(
        self,
        metrics_before: Dict[str, float],
        metrics_after: Dict[str, float]
    ) -> float:
        """Calculate performance improvement percentage"""
        try:
            if not metrics_before or not metrics_after:
                return 0.0
            
            improvements = []
            
            for metric_name in metrics_before.keys():
                if metric_name in metrics_after:
                    before = metrics_before[metric_name]
                    after = metrics_after[metric_name]
                    
                    if before > 0:
                        improvement = ((before - after) / before) * 100
                        improvements.append(improvement)
            
            return statistics.mean(improvements) if improvements else 0.0
            
        except Exception:
            return 0.0
    
    async def _auto_optimize(self):
        """Automatic optimization based on current metrics"""
        try:
            # Check if optimization is needed
            for metric, value in self.current_metrics.items():
                threshold = self.thresholds.get(metric)
                if threshold and threshold.auto_optimize:
                    if value > threshold.critical_threshold:
                        await self.optimize_performance(threshold.optimization_strategy, [metric])
                        break  # Only one optimization per cycle
                        
        except Exception as e:
            self.log_error(f"Auto optimization failed: {e}")

    async def _analyze_metrics(self, cutoff_time: datetime) -> Dict[str, Any]:
        """Analyze performance metrics"""
        try:
            total_recent_metrics = 0
            metric_summaries = {}

            # Analyze each metric type
            for metric_type, metric_data in self.metrics_history.items():
                # Filter by cutoff time - metric_data is List[Tuple[datetime, float]]
                recent_data = [
                    (timestamp, value) for timestamp, value in metric_data
                    if timestamp >= cutoff_time
                ]

                if recent_data:
                    values = [value for _, value in recent_data]
                    metric_summaries[metric_type.value] = {
                        "count": len(recent_data),
                        "average": sum(values) / len(values),
                        "min": min(values),
                        "max": max(values)
                    }
                    total_recent_metrics += len(recent_data)

            if total_recent_metrics == 0:
                return {"total_metrics": 0, "analysis": "No recent metrics"}

            return {
                "total_metrics": total_recent_metrics,
                "metric_summaries": metric_summaries,
                "analysis": "Metrics analyzed successfully"
            }

        except Exception as e:
            self.log_error(f"Metrics analysis failed: {e}")
            return {"error": str(e)}

    async def _generate_optimization_recommendations(
        self,
        strategy: str,
        metrics_before: Dict[str, Any],
        metrics_after: Dict[str, Any]
    ) -> List[str]:
        """Generate optimization recommendations"""
        try:
            recommendations = []

            # Analyze performance changes
            if metrics_after.get("response_time", 0) > metrics_before.get("response_time", 0):
                recommendations.append("Consider optimizing response time")

            if metrics_after.get("memory_usage", 0) > metrics_before.get("memory_usage", 0):
                recommendations.append("Memory usage increased - consider cleanup")

            if metrics_after.get("cpu_usage", 0) > metrics_before.get("cpu_usage", 0):
                recommendations.append("CPU usage increased - consider load balancing")

            # Strategy-specific recommendations
            if strategy == "memory_optimization":
                recommendations.append("Continue memory optimization strategies")
            elif strategy == "response_time_optimization":
                recommendations.append("Focus on response time improvements")

            return recommendations if recommendations else ["Performance is optimal"]

        except Exception as e:
            self.log_error(f"Recommendation generation failed: {e}")
            return [f"Error generating recommendations: {e}"]

    async def _analyze_optimizations(self, cutoff_time: datetime) -> Dict[str, Any]:
        """Analyze optimization history"""
        try:
            # Filter optimizations by cutoff time
            recent_optimizations = [
                opt for opt in self.optimization_history
                if opt.timestamp >= cutoff_time
            ]

            if not recent_optimizations:
                return {"total_optimizations": 0, "analysis": "No recent optimizations"}

            # Analyze optimization results
            successful_optimizations = [opt for opt in recent_optimizations if opt.success]
            failed_optimizations = [opt for opt in recent_optimizations if not opt.success]

            # Calculate average improvement
            improvements = [opt.improvement_percentage for opt in successful_optimizations if opt.improvement_percentage > 0]
            avg_improvement = sum(improvements) / len(improvements) if improvements else 0

            # Group by strategy
            strategy_counts = {}
            for opt in recent_optimizations:
                strategy = opt.strategy.value
                if strategy not in strategy_counts:
                    strategy_counts[strategy] = {"total": 0, "successful": 0}
                strategy_counts[strategy]["total"] += 1
                if opt.success:
                    strategy_counts[strategy]["successful"] += 1

            return {
                "total_optimizations": len(recent_optimizations),
                "successful_optimizations": len(successful_optimizations),
                "failed_optimizations": len(failed_optimizations),
                "success_rate": len(successful_optimizations) / len(recent_optimizations) * 100,
                "average_improvement": avg_improvement,
                "strategy_breakdown": strategy_counts,
                "analysis": "Optimizations analyzed successfully"
            }

        except Exception as e:
            self.log_error(f"Optimization analysis failed: {e}")
            return {"error": str(e)}

    async def _detect_bottlenecks(self, cutoff_time: datetime) -> List[Dict[str, Any]]:
        """Detect performance bottlenecks"""
        try:
            bottlenecks = []

            # Analyze each metric type for bottlenecks
            for metric_type, metric_data in self.metrics_history.items():
                # Filter by cutoff time
                recent_data = [
                    (timestamp, value) for timestamp, value in metric_data
                    if timestamp >= cutoff_time
                ]

                if recent_data:
                    values = [value for _, value in recent_data]
                    avg_value = sum(values) / len(values)
                    max_value = max(values)

                    # Define bottleneck thresholds
                    thresholds = {
                        PerformanceMetric.RESPONSE_TIME: 2.0,  # 2 seconds
                        PerformanceMetric.ERROR_RATE: 0.05,    # 5%
                        PerformanceMetric.RESOURCE_UTILIZATION: 0.8,  # 80%
                        PerformanceMetric.THROUGHPUT: 10.0     # 10 requests/sec minimum
                    }

                    threshold = thresholds.get(metric_type, 1.0)

                    # Check for bottlenecks
                    if metric_type == PerformanceMetric.THROUGHPUT:
                        # For throughput, low values are bad
                        if avg_value < threshold:
                            bottlenecks.append({
                                "type": "low_throughput",
                                "metric": metric_type.value,
                                "current_value": avg_value,
                                "threshold": threshold,
                                "severity": "high" if avg_value < threshold * 0.5 else "medium",
                                "recommendation": "Increase concurrency or optimize processing"
                            })
                    else:
                        # For other metrics, high values are bad
                        if avg_value > threshold or max_value > threshold * 1.5:
                            severity = "high" if max_value > threshold * 2 else "medium"
                            bottlenecks.append({
                                "type": "high_" + metric_type.value,
                                "metric": metric_type.value,
                                "current_value": avg_value,
                                "max_value": max_value,
                                "threshold": threshold,
                                "severity": severity,
                                "recommendation": self._get_bottleneck_recommendation(metric_type)
                            })

            return bottlenecks

        except Exception as e:
            self.log_error(f"Bottleneck detection failed: {e}")
            return [{"error": str(e)}]

    def _get_bottleneck_recommendation(self, metric_type: PerformanceMetric) -> str:
        """Get recommendation for specific bottleneck type"""
        recommendations = {
            PerformanceMetric.RESPONSE_TIME: "Optimize database queries, add caching, or scale horizontally",
            PerformanceMetric.ERROR_RATE: "Review error logs, improve error handling, and fix underlying issues",
            PerformanceMetric.RESOURCE_UTILIZATION: "Scale resources, optimize memory usage, or improve algorithms",
            PerformanceMetric.THROUGHPUT: "Increase concurrency, optimize processing, or add load balancing"
        }
        return recommendations.get(metric_type, "Review and optimize system performance")

    async def _generate_performance_recommendations(self) -> List[str]:
        """Generate general performance recommendations"""
        try:
            recommendations = []

            # Analyze current metrics
            for metric_type, current_value in self.current_metrics.items():
                if metric_type == PerformanceMetric.RESPONSE_TIME and current_value > 1.0:
                    recommendations.append("Consider optimizing response times - current average exceeds 1 second")
                elif metric_type == PerformanceMetric.ERROR_RATE and current_value > 0.02:
                    recommendations.append("Error rate is elevated - review error logs and improve error handling")
                elif metric_type == PerformanceMetric.RESOURCE_UTILIZATION and current_value > 0.7:
                    recommendations.append("Resource utilization is high - consider scaling or optimization")
                elif metric_type == PerformanceMetric.THROUGHPUT and current_value < 5.0:
                    recommendations.append("Throughput is low - consider performance optimizations")

            # Add general recommendations if no specific issues found
            if not recommendations:
                recommendations.extend([
                    "System performance appears optimal",
                    "Continue monitoring for potential improvements",
                    "Consider implementing caching strategies",
                    "Review and optimize database queries periodically"
                ])

            return recommendations

        except Exception as e:
            self.log_error(f"Performance recommendation generation failed: {e}")
            return [f"Error generating recommendations: {e}"]

    async def _get_resource_usage(self) -> Dict[str, Any]:
        """Get current resource usage statistics"""
        try:
            import psutil

            # Get system resource usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Get process-specific usage
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()

            return {
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_percent": disk.percent,
                    "disk_free_gb": disk.free / (1024**3)
                },
                "process": {
                    "cpu_percent": process_cpu,
                    "memory_mb": process_memory.rss / (1024**2),
                    "memory_percent": process.memory_percent(),
                    "threads": process.num_threads()
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.log_error(f"Resource usage collection failed: {e}")
            return {"error": str(e)}

    async def _calculate_performance_score(self) -> float:
        """Calculate overall performance score (0-100)"""
        try:
            if not self.current_metrics:
                return 50.0  # Neutral score if no metrics

            scores = []

            # Score each metric (higher is better, scale to 0-100)
            for metric_type, value in self.current_metrics.items():
                if metric_type == PerformanceMetric.RESPONSE_TIME:
                    # Lower response time is better (invert score)
                    score = max(0, 100 - (value * 50))  # 2 seconds = 0 score
                elif metric_type == PerformanceMetric.ERROR_RATE:
                    # Lower error rate is better (invert score)
                    score = max(0, 100 - (value * 2000))  # 5% error = 0 score
                elif metric_type == PerformanceMetric.RESOURCE_UTILIZATION:
                    # Moderate utilization is best (bell curve)
                    optimal = 0.6  # 60% utilization is optimal
                    score = max(0, 100 - abs(value - optimal) * 200)
                elif metric_type == PerformanceMetric.THROUGHPUT:
                    # Higher throughput is better
                    score = min(100, value * 10)  # 10 req/sec = 100 score
                else:
                    score = 50.0  # Neutral for unknown metrics

                scores.append(score)

            # Calculate weighted average
            overall_score = sum(scores) / len(scores) if scores else 50.0
            return round(overall_score, 2)

        except Exception as e:
            self.log_error(f"Performance score calculation failed: {e}")
            return 50.0  # Return neutral score on error


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
