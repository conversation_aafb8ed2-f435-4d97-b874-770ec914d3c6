"""
Test script for Verification Agent
Tests Google Search integration, parallel processing, and result validation
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from app.services.verification_agent import VerificationAgent
from app.services.google_search_integration import GoogleSearchIntegration
from app.services.parallel_processor import parallel_processor, ParallelWorkflow
from app.models.browser_automation import VerificationResult


async def test_verification_agent():
    """Test the verification agent with various scenarios"""
    
    print("🧪 Testing Verification Agent")
    print("=" * 50)
    
    # Initialize verification agent
    verification_agent = VerificationAgent()
    
    try:
        # Test 1: Agent Initialization
        print("\n1️⃣ Testing Agent Initialization...")
        try:
            await verification_agent.initialize()
            print("✅ Verification agent initialized successfully")
        except Exception as e:
            print(f"❌ Verification agent initialization failed: {e}")
            print("   Note: This may fail if Google Search API is not configured")
            return
        
        # Test 2: Basic Information Verification
        print("\n2️⃣ Testing Basic Information Verification...")
        try:
            primary_data = "The capital of France is Paris"
            context = {
                "source_domain": "example.com",
                "task_type": "fact_verification"
            }
            
            verification_result = await verification_agent.verify_information(
                primary_data=primary_data,
                context=context,
                verification_queries=["capital of France", "Paris France capital"],
                enable_parallel=True
            )
            
            print("✅ Basic verification completed")
            print(f"   Verification status: {verification_result.verification_status}")
            print(f"   Confidence score: {verification_result.confidence_score}")
            print(f"   Sources checked: {len(verification_result.google_search_results)}")
            
        except Exception as e:
            print(f"❌ Basic verification failed: {e}")
        
        # Test 3: Price Information Verification
        print("\n3️⃣ Testing Price Information Verification...")
        try:
            product_name = "iPhone 15 Pro"
            claimed_price = "$999"
            source_url = "https://example-store.com"
            
            price_verification = await verification_agent.verify_price_information(
                product_name=product_name,
                claimed_price=claimed_price,
                source_url=source_url
            )
            
            print("✅ Price verification completed")
            print(f"   Verification status: {price_verification.verification_status}")
            print(f"   Confidence score: {price_verification.confidence_score}")
            print(f"   Cross-references: {len(price_verification.cross_references)}")
            
        except Exception as e:
            print(f"❌ Price verification failed: {e}")
        
        # Test 4: Factual Claim Verification
        print("\n4️⃣ Testing Factual Claim Verification...")
        try:
            claim = "Python is a programming language created by Guido van Rossum"
            domain = "technology"
            
            fact_verification = await verification_agent.verify_factual_claim(
                claim=claim,
                domain=domain
            )
            
            print("✅ Factual claim verification completed")
            print(f"   Verification status: {fact_verification.verification_status}")
            print(f"   Confidence score: {fact_verification.confidence_score}")
            print(f"   Sources analyzed: {len(fact_verification.google_search_results)}")
            
        except Exception as e:
            print(f"❌ Factual claim verification failed: {e}")
        
        # Test 5: Cross-Reference Sources
        print("\n5️⃣ Testing Cross-Reference Sources...")
        try:
            primary_source = {
                "content": "Artificial Intelligence is transforming industries",
                "url": "https://primary-source.com",
                "credibility": 0.8
            }
            
            additional_sources = [
                {
                    "content": "AI is revolutionizing business operations across sectors",
                    "url": "https://tech-news.com",
                    "credibility": 0.7
                },
                {
                    "content": "Machine learning applications are growing rapidly",
                    "url": "https://research-journal.com",
                    "credibility": 0.9
                }
            ]
            
            cross_ref_result = await verification_agent.cross_reference_sources(
                primary_source=primary_source,
                additional_sources=additional_sources
            )
            
            print("✅ Cross-reference analysis completed")
            print(f"   Consistency score: {cross_ref_result.get('consistency_score', 0)}")
            print(f"   Supporting sources: {len(cross_ref_result.get('supporting_sources', []))}")
            print(f"   Conflicting sources: {len(cross_ref_result.get('conflicting_sources', []))}")
            
        except Exception as e:
            print(f"❌ Cross-reference analysis failed: {e}")
        
        # Cleanup
        await verification_agent.cleanup()
        print("✅ Verification agent cleanup completed")
        
    except Exception as e:
        print(f"❌ Verification agent test setup failed: {e}")


async def test_google_search_integration():
    """Test Google Search integration functionality"""
    
    print("\n🔍 Testing Google Search Integration")
    print("-" * 40)
    
    # Initialize Google Search integration
    google_search = GoogleSearchIntegration()
    
    try:
        # Test 1: Search Integration Initialization
        print("\n1️⃣ Testing Search Integration Initialization...")
        try:
            await google_search.initialize()
            print("✅ Google Search integration initialized")
        except Exception as e:
            print(f"❌ Google Search integration failed: {e}")
            print("   Note: This requires valid Google Search API credentials")
            return
        
        # Test 2: Basic Search
        print("\n2️⃣ Testing Basic Search...")
        try:
            search_result = await google_search.search(
                query="Python programming language",
                search_type="general",
                num_results=5
            )
            
            print("✅ Basic search completed")
            print(f"   Query: {search_result['query']}")
            print(f"   Results found: {len(search_result.get('items', []))}")
            print(f"   Search time: {search_result.get('search_time', 0)}s")
            
        except Exception as e:
            print(f"❌ Basic search failed: {e}")
        
        # Test 3: News Search
        print("\n3️⃣ Testing News Search...")
        try:
            news_result = await google_search.search_news(
                query="artificial intelligence latest news",
                time_period="d1",
                num_results=3
            )
            
            print("✅ News search completed")
            print(f"   News articles found: {len(news_result.get('items', []))}")
            
        except Exception as e:
            print(f"❌ News search failed: {e}")
        
        # Test 4: Shopping Search
        print("\n4️⃣ Testing Shopping Search...")
        try:
            shopping_result = await google_search.search_shopping(
                product_query="laptop computer",
                price_range="$500-$1000",
                num_results=5
            )
            
            print("✅ Shopping search completed")
            print(f"   Product results found: {len(shopping_result.get('items', []))}")
            
        except Exception as e:
            print(f"❌ Shopping search failed: {e}")
        
        # Test 5: Multiple Queries
        print("\n5️⃣ Testing Multiple Queries...")
        try:
            queries = [
                "machine learning basics",
                "data science tools",
                "cloud computing trends"
            ]
            
            multiple_results = await google_search.search_multiple_queries(
                queries=queries,
                search_type="general",
                max_concurrent=2
            )
            
            print("✅ Multiple queries search completed")
            print(f"   Queries processed: {len(multiple_results)}")
            
            for i, result in enumerate(multiple_results):
                if "items" in result:
                    print(f"   Query {i+1}: {len(result['items'])} results")
                else:
                    print(f"   Query {i+1}: Error - {result.get('error', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Multiple queries search failed: {e}")
        
        # Test 6: Search Insights
        print("\n6️⃣ Testing Search Insights...")
        try:
            # Use previous search result for insights
            if 'search_result' in locals():
                insights = await google_search.extract_search_insights(
                    search_results=search_result,
                    focus_area="technology"
                )
                
                print("✅ Search insights extraction completed")
                print(f"   Total results analyzed: {insights.get('total_results', 0)}")
                print(f"   Domains found: {len(insights.get('domains_found', []))}")
                print(f"   Common themes: {insights.get('common_themes', [])}")
                
        except Exception as e:
            print(f"❌ Search insights extraction failed: {e}")
        
        # Cleanup
        await google_search.cleanup()
        print("✅ Google Search integration cleanup completed")
        
    except Exception as e:
        print(f"❌ Google Search integration test failed: {e}")


async def test_parallel_processing():
    """Test parallel processing functionality"""
    
    print("\n⚡ Testing Parallel Processing")
    print("-" * 40)
    
    try:
        # Test 1: Basic Parallel Execution
        print("\n1️⃣ Testing Basic Parallel Execution...")
        
        # Create mock verification tasks
        async def mock_verification_task(query: str, delay: float = 1.0) -> Dict[str, Any]:
            await asyncio.sleep(delay)
            return {
                "query": query,
                "results": [f"Result for {query}"],
                "success": True,
                "execution_time": delay
            }
        
        verification_tasks = [
            {
                "function": mock_verification_task,
                "params": {"query": "test query 1", "delay": 0.5}
            },
            {
                "function": mock_verification_task,
                "params": {"query": "test query 2", "delay": 0.3}
            },
            {
                "function": mock_verification_task,
                "params": {"query": "test query 3", "delay": 0.7}
            }
        ]
        
        results = await parallel_processor.execute_verification_tasks(
            verification_tasks=verification_tasks,
            max_concurrent=2,
            timeout=5.0
        )
        
        print("✅ Basic parallel execution completed")
        print(f"   Tasks executed: {len(results)}")
        print(f"   Successful tasks: {sum(1 for r in results if r.success)}")
        print(f"   Failed tasks: {sum(1 for r in results if not r.success)}")
        
        # Test 2: Parallel Workflow
        print("\n2️⃣ Testing Parallel Workflow...")
        
        workflow = ParallelWorkflow(
            workflow_id="test_workflow_001",
            tasks=verification_tasks,
            max_concurrent=3,
            timeout=10.0,
            retry_attempts=1,
            failure_threshold=0.2
        )
        
        # Progress callback
        async def progress_callback(progress_data: Dict[str, Any]):
            print(f"   Progress: {progress_data['completed']}/{progress_data['total']} "
                  f"({progress_data['progress']:.1f}%)")
        
        workflow_result = await parallel_processor.execute_parallel_workflow(
            workflow=workflow,
            progress_callback=progress_callback
        )
        
        print("✅ Parallel workflow completed")
        print(f"   Workflow success: {workflow_result['workflow_success']}")
        print(f"   Success rate: {workflow_result['success_rate']:.2f}")
        print(f"   Execution time: {workflow_result['execution_time']:.2f}s")
        
        # Test 3: Dependency-Based Execution
        print("\n3️⃣ Testing Dependency-Based Execution...")
        
        tasks_with_deps = {
            "task_a": {
                "function": mock_verification_task,
                "params": {"query": "independent task A", "delay": 0.2},
                "dependencies": []
            },
            "task_b": {
                "function": mock_verification_task,
                "params": {"query": "depends on A", "delay": 0.3},
                "dependencies": ["task_a"]
            },
            "task_c": {
                "function": mock_verification_task,
                "params": {"query": "depends on A and B", "delay": 0.1},
                "dependencies": ["task_a", "task_b"]
            }
        }
        
        dep_results = await parallel_processor.execute_with_dependencies(
            tasks_with_deps=tasks_with_deps,
            max_concurrent=2
        )
        
        print("✅ Dependency-based execution completed")
        print(f"   Tasks with dependencies: {len(dep_results)}")
        
        for task_id, result in dep_results.items():
            print(f"   {task_id}: {'✅' if result.success else '❌'}")
        
        # Test 4: Execution Statistics
        print("\n4️⃣ Testing Execution Statistics...")
        
        stats = parallel_processor.get_execution_stats()
        print("✅ Execution statistics retrieved")
        print(f"   Total workflows: {stats['total_workflows']}")
        print(f"   Total tasks: {stats['total_tasks']}")
        print(f"   Success rate: {stats['successful_tasks']}/{stats['total_tasks']}")
        print(f"   Average execution time: {stats['average_execution_time']:.2f}s")
        
    except Exception as e:
        print(f"❌ Parallel processing test failed: {e}")


async def test_integration_scenarios():
    """Test integrated verification scenarios"""
    
    print("\n🔗 Testing Integration Scenarios")
    print("-" * 40)
    
    try:
        # Scenario 1: E-commerce Price Verification
        print("\n1️⃣ E-commerce Price Verification Scenario...")
        
        # Mock browser automation result
        browser_result = {
            "product_name": "MacBook Pro 16-inch",
            "price": "$2,399",
            "source_url": "https://apple.com",
            "features": ["M3 Pro chip", "16GB RAM", "512GB SSD"]
        }
        
        # Mock verification workflow
        print("   📊 Browser found product information")
        print(f"   🔍 Verifying price: {browser_result['price']}")
        print("   ✅ Price verification would be performed in parallel")
        print("   📈 Confidence score would be calculated")
        
        # Scenario 2: News Article Fact-Checking
        print("\n2️⃣ News Article Fact-Checking Scenario...")
        
        news_claim = "New AI model achieves 95% accuracy on medical diagnosis"
        
        print(f"   📰 Claim to verify: {news_claim}")
        print("   🔍 Searching academic sources...")
        print("   📊 Cross-referencing with medical journals...")
        print("   ✅ Fact-checking would be performed with high credibility sources")
        
        # Scenario 3: Multi-Source Research Verification
        print("\n3️⃣ Multi-Source Research Verification Scenario...")
        
        research_topic = "Climate change impact on agriculture"
        
        print(f"   🔬 Research topic: {research_topic}")
        print("   📚 Searching academic databases...")
        print("   🏛️ Checking government sources...")
        print("   📰 Reviewing recent news articles...")
        print("   ✅ Comprehensive verification would synthesize multiple source types")
        
        print("\n✅ All integration scenarios outlined successfully")
        
    except Exception as e:
        print(f"❌ Integration scenarios test failed: {e}")


if __name__ == "__main__":
    print("🚀 RouKey Verification Agent Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    asyncio.run(test_verification_agent())
    asyncio.run(test_google_search_integration())
    asyncio.run(test_parallel_processing())
    asyncio.run(test_integration_scenarios())
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 All verification tests completed!")
    print("\n📝 Note: Some tests may show failures if Google Search API is not configured.")
    print("   Configure GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_ENGINE_ID in .env file for full functionality.")
