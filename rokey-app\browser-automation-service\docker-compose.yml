version: '3.8'

services:
  browser-automation:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8000
      - SERVICE_ENV=development
      - DEBUG=true
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - DATABASE_URL=********************************************/rokey_db
    depends_on:
      - redis
      - qdrant
      - postgres
    volumes:
      - ./app:/app/app
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - rokey-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - rokey-network

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
    restart: unless-stopped
    networks:
      - rokey-network

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=rokey_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - rokey-network

volumes:
  redis_data:
  qdrant_data:
  postgres_data:

networks:
  rokey-network:
    driver: bridge
