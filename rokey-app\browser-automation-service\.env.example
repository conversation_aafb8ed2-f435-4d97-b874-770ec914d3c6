# Browser Automation Service Environment Variables

# Service Configuration
SERVICE_HOST=localhost
SERVICE_PORT=8000
SERVICE_ENV=development
DEBUG=true

# RouKey Integration
ROKEY_API_URL=http://localhost:3000
ROKEY_API_SECRET=your-rokey-api-secret-here

# Database Configuration (PostgreSQL for RouKey integration)
DATABASE_URL=postgresql://username:password@localhost:5432/rokey_db

# Redis Configuration (for session management and caching)
REDIS_URL=redis://localhost:6379/0
REDIS_SESSION_DB=1
REDIS_CACHE_DB=2

# LLM API Keys (for Browser Use agents)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
GROK_API_KEY=your-grok-api-key

# Google Custom Search API
GOOGLE_SEARCH_API_KEY=your-google-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id

# Memory Configuration (Qdrant Vector Store)
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=your-qdrant-api-key-if-cloud

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080

# Session Management
SESSION_POOL_SIZE=10
SESSION_TIMEOUT=300
MAX_CONCURRENT_SESSIONS=5

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security
JWT_SECRET_KEY=your-jwt-secret-key
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Performance
MAX_WORKERS=4
REQUEST_TIMEOUT=300
RATE_LIMIT_PER_MINUTE=60
