"""
Test script for Browser Execution Agent
Tests all browser automation capabilities and memory integration
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from app.services.browser_execution_agent import BrowserExecutionAgent
from app.services.browser_pool import BrowserPool
from app.services.session_manager import Session<PERSON>anager
from app.services.memory_integration import memory_integration
from app.models.browser_automation import TodoItem, TaskStatus


async def test_browser_execution_agent():
    """Test the browser execution agent with various scenarios"""
    
    print("🧪 Testing Browser Execution Agent")
    print("=" * 50)
    
    # Initialize services
    session_manager = SessionManager()
    browser_pool = BrowserPool()
    
    try:
        # Initialize services
        await session_manager.initialize()
        await browser_pool.initialize()
        await memory_integration.initialize()
        
        print("✅ Services initialized successfully")
        
        # Test configuration
        test_llm_config = {
            "provider": "openai",
            "api_key": "test-key",  # Mock key for testing
            "model": "gpt-4o",
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        # Create browser execution agent
        agent = BrowserExecutionAgent(
            role="web_research",
            llm_config=test_llm_config,
            session_manager=session_manager,
            browser_pool=browser_pool,
            enable_memory=True
        )
        
        print("✅ Browser execution agent created")
        
        # Test 1: Agent Initialization
        print("\n1️⃣ Testing Agent Initialization...")
        session_id = "test_session_001"
        task_description = "Navigate to Google and search for information"
        
        try:
            await agent.initialize(session_id, task_description)
            print("✅ Agent initialization successful")
            print(f"   Session ID: {session_id}")
            print(f"   Current URL: {agent.current_page.url if agent.current_page else 'None'}")
        except Exception as e:
            print(f"❌ Agent initialization failed: {e}")
            return
        
        # Test 2: Basic Navigation
        print("\n2️⃣ Testing Basic Navigation...")
        try:
            navigation_result = await agent.navigate_to_url("https://www.google.com")
            print("✅ Navigation successful")
            print(f"   Result: {navigation_result['status']}")
            print(f"   Page title: {navigation_result.get('page_title', 'N/A')}")
        except Exception as e:
            print(f"❌ Navigation failed: {e}")
        
        # Test 3: Screenshot Capability
        print("\n3️⃣ Testing Screenshot Capability...")
        try:
            screenshot_path = await agent.take_screenshot("test_google_homepage")
            print("✅ Screenshot taken successfully")
            print(f"   Screenshot path: {screenshot_path}")
        except Exception as e:
            print(f"❌ Screenshot failed: {e}")
        
        # Test 4: Todo Item Execution
        print("\n4️⃣ Testing Todo Item Execution...")
        try:
            todo_item = TodoItem(
                id="test_todo_001",
                task="Get the page title and URL",
                priority=5,
                assigned_agent="web_research",
                status=TaskStatus.PENDING
            )
            
            todo_result = await agent.execute_todo_item(todo_item)
            print("✅ Todo item execution successful")
            print(f"   Todo ID: {todo_result['todo_id']}")
            print(f"   Status: {todo_result['status']}")
        except Exception as e:
            print(f"❌ Todo item execution failed: {e}")
        
        # Test 5: Data Extraction
        print("\n5️⃣ Testing Data Extraction...")
        try:
            extraction_config = {
                "type": "page_info",
                "selectors": ["title", "h1", "h2", "a[href]"]
            }
            
            extraction_result = await agent.extract_data(extraction_config)
            print("✅ Data extraction successful")
            print(f"   Extraction type: {extraction_result['extraction_type']}")
            print(f"   Source URL: {extraction_result['source_url']}")
        except Exception as e:
            print(f"❌ Data extraction failed: {e}")
        
        # Test 6: Memory Integration
        print("\n6️⃣ Testing Memory Integration...")
        try:
            # Store some test memories
            await memory_integration.store_memory(
                agent_id="web_research",
                memory_type="action",
                content="Successfully navigated to Google homepage",
                importance=0.8
            )
            
            await memory_integration.store_memory(
                agent_id="web_research",
                memory_type="finding",
                content="Google homepage loaded successfully with search functionality",
                importance=0.7
            )
            
            # Retrieve memories
            memories = await memory_integration.retrieve_memories(
                agent_id="web_research",
                limit=5
            )
            
            print("✅ Memory integration successful")
            print(f"   Stored memories: 2")
            print(f"   Retrieved memories: {len(memories)}")
            
            for i, memory in enumerate(memories):
                print(f"   Memory {i+1}: {memory['type']} - {memory['content'][:50]}...")
                
        except Exception as e:
            print(f"❌ Memory integration failed: {e}")
        
        # Test 7: Streaming Execution
        print("\n7️⃣ Testing Streaming Execution...")
        try:
            stream_task = "Analyze the current page structure"
            stream_count = 0
            
            async for update in agent.execute_with_streaming(stream_task, max_steps=5):
                stream_count += 1
                print(f"   Stream update {stream_count}: {update['type']} - {update.get('message', update.get('current_action', 'N/A'))}")
                
                if stream_count >= 10:  # Limit output
                    break
            
            print("✅ Streaming execution successful")
            print(f"   Total stream updates: {stream_count}")
            
        except Exception as e:
            print(f"❌ Streaming execution failed: {e}")
        
        # Test 8: Error Handling
        print("\n8️⃣ Testing Error Handling...")
        try:
            # Try to navigate to invalid URL
            try:
                await agent.navigate_to_url("https://invalid-url-that-does-not-exist.com")
            except Exception as nav_error:
                print("✅ Error handling working correctly")
                print(f"   Caught expected error: {type(nav_error).__name__}")
            
            # Check error metadata
            error_count = len(agent.execution_metadata.get("errors_encountered", []))
            print(f"   Errors recorded in metadata: {error_count}")
            
        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
        
        # Test 9: Session State Management
        print("\n9️⃣ Testing Session State Management...")
        try:
            # Get current session state
            current_session = agent.current_session
            if current_session:
                print("✅ Session state management working")
                print(f"   Session ID: {current_session.session_id}")
                print(f"   Current URL: {current_session.current_url}")
                print(f"   Created at: {current_session.created_at}")
            else:
                print("❌ No active session found")
                
        except Exception as e:
            print(f"❌ Session state management failed: {e}")
        
        # Test 10: Execution Metadata
        print("\n🔟 Testing Execution Metadata...")
        try:
            metadata = agent.execution_metadata
            print("✅ Execution metadata available")
            print(f"   Session ID: {metadata.get('session_id')}")
            print(f"   Steps completed: {metadata.get('steps_completed', 0)}")
            print(f"   Actions performed: {len(metadata.get('actions_performed', []))}")
            print(f"   Pages visited: {len(metadata.get('pages_visited', []))}")
            print(f"   Screenshots taken: {len(metadata.get('screenshots_taken', []))}")
            print(f"   Errors encountered: {len(metadata.get('errors_encountered', []))}")
            
        except Exception as e:
            print(f"❌ Execution metadata test failed: {e}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        try:
            await agent.cleanup()
            print("✅ Agent cleanup successful")
        except Exception as e:
            print(f"❌ Agent cleanup failed: {e}")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        
    finally:
        # Cleanup services
        try:
            await session_manager.cleanup()
            await browser_pool.cleanup()
            await memory_integration.cleanup()
            print("✅ Services cleanup completed")
        except Exception as e:
            print(f"❌ Services cleanup failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Browser Execution Agent Testing Complete!")


async def test_browser_actions():
    """Test individual browser actions"""
    
    print("\n🔧 Testing Individual Browser Actions")
    print("-" * 40)
    
    # This would test the BrowserActions class
    # For now, just show what would be tested
    
    test_scenarios = [
        "Navigation and waiting",
        "Element clicking and interaction",
        "Text input and form filling",
        "Page scrolling and viewport management",
        "Content extraction and analysis",
        "Link extraction and filtering",
        "Calendar and date picker handling",
        "Screenshot and visual verification",
        "Multi-element operations",
        "Error recovery and retry logic"
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"   {i}. {scenario} - Would be tested")
    
    print("✅ Browser actions test scenarios identified")


async def test_memory_functionality():
    """Test memory functionality in detail"""
    
    print("\n🧠 Testing Memory Functionality")
    print("-" * 40)
    
    try:
        await memory_integration.initialize()
        
        # Test memory storage and retrieval
        agent_id = "test_memory_agent"
        
        # Store different types of memories
        memory_types = ["action", "finding", "error", "context"]
        stored_memories = []
        
        for i, mem_type in enumerate(memory_types):
            memory_id = await memory_integration.store_memory(
                agent_id=agent_id,
                memory_type=mem_type,
                content=f"Test {mem_type} memory content {i+1}",
                importance=0.5 + (i * 0.1)
            )
            stored_memories.append(memory_id)
        
        print(f"✅ Stored {len(stored_memories)} test memories")
        
        # Retrieve memories
        retrieved = await memory_integration.retrieve_memories(agent_id, limit=10)
        print(f"✅ Retrieved {len(retrieved)} memories")
        
        # Get memory stats
        stats = await memory_integration.get_memory_stats(agent_id)
        print(f"✅ Memory stats: {stats}")
        
        # Test memory clearing
        cleared = await memory_integration.clear_agent_memory(agent_id, memory_type="action")
        print(f"✅ Cleared {cleared} action memories")
        
        await memory_integration.cleanup()
        
    except Exception as e:
        print(f"❌ Memory functionality test failed: {e}")


if __name__ == "__main__":
    print("🚀 RouKey Browser Execution Agent Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    asyncio.run(test_browser_execution_agent())
    asyncio.run(test_browser_actions())
    asyncio.run(test_memory_functionality())
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 All browser execution tests completed!")
