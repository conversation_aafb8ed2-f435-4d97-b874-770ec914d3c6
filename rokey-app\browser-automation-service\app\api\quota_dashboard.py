"""
Quota Dashboard API
Provides endpoints for users to view their subscription tier, quota usage, and upgrade options
"""

from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel

from app.core.logging import LoggerMixin
from app.services.access_control import access_control_manager, SubscriptionTier, FeatureType


class QuotaUsageResponse(BaseModel):
    """Response model for quota usage information"""
    user_id: str
    subscription_tier: str
    current_period: Dict[str, Any]
    limits: Dict[str, int]
    current_usage: Dict[str, int]
    usage_percentages: Dict[str, float]
    overages: Dict[str, int]
    available_features: list[str]
    priority_level: int
    support_level: str


class UpgradeSimulationResponse(BaseModel):
    """Response model for tier upgrade simulation"""
    current_tier: str
    target_tier: str
    improvements: Dict[str, Dict[str, Any]]
    new_features: list[str]
    support_upgrade: str
    priority_upgrade: str


class AccessCheckResponse(BaseModel):
    """Response model for feature access check"""
    access_granted: bool
    tier: str
    feature: str
    reason: Optional[str] = None
    required_tier: Optional[str] = None
    quota_status: Optional[Dict[str, Any]] = None


class QuotaDashboardAPI(LoggerMixin):
    """API endpoints for quota dashboard functionality"""
    
    def __init__(self):
        self.router = APIRouter(prefix="/api/quota", tags=["quota"])
        self._setup_routes()
        self.log_info("Quota dashboard API initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.router.get("/usage/{user_id}", response_model=QuotaUsageResponse)
        async def get_quota_usage(user_id: str):
            """
            Get comprehensive quota usage information for a user
            
            Args:
                user_id: User identifier
                
            Returns:
                Detailed quota usage and tier information
            """
            try:
                self.log_info(f"Getting quota usage for user: {user_id}")
                
                user_limits = await access_control_manager.get_user_limits(user_id)
                
                if "error" in user_limits:
                    raise HTTPException(
                        status_code=404,
                        detail=f"User quota not found: {user_limits['error']}"
                    )
                
                return QuotaUsageResponse(**user_limits)
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get quota usage: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve quota usage: {e}"
                )
        
        @self.router.get("/browsing-status/{user_id}")
        async def get_browsing_quota_status(user_id: str):
            """
            Get specific browsing quota status for a user
            
            Args:
                user_id: User identifier
                
            Returns:
                Browsing quota status and availability
            """
            try:
                self.log_info(f"Getting browsing quota status for user: {user_id}")
                
                quota_status = await access_control_manager.check_browsing_quota(user_id)
                
                return {
                    "user_id": user_id,
                    "can_browse": quota_status["can_browse"],
                    "remaining_tasks": quota_status["remaining_tasks"],
                    "current_usage": quota_status.get("current_usage", 0),
                    "quota_limit": quota_status.get("quota_limit", 0),
                    "percentage_used": quota_status.get("percentage_used", 0),
                    "reset_date": quota_status.get("reset_date"),
                    "reason": quota_status.get("reason"),
                    "required_tier": quota_status.get("required_tier")
                }
                
            except Exception as e:
                self.log_error(f"Failed to get browsing quota status: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve browsing quota status: {e}"
                )
        
        @self.router.post("/check-access/{user_id}", response_model=AccessCheckResponse)
        async def check_feature_access(
            user_id: str,
            feature: str,
            resource_requirements: Optional[Dict[str, Any]] = None
        ):
            """
            Check if user has access to a specific feature
            
            Args:
                user_id: User identifier
                feature: Feature to check access for
                resource_requirements: Optional resource requirements
                
            Returns:
                Access check result with details
            """
            try:
                self.log_info(f"Checking feature access for user: {user_id}, feature: {feature}")
                
                # Validate feature
                try:
                    feature_enum = FeatureType(feature)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid feature: {feature}"
                    )
                
                access_result = await access_control_manager.check_access(
                    user_id=user_id,
                    feature=feature_enum,
                    resource_requirements=resource_requirements or {}
                )
                
                return AccessCheckResponse(**access_result)
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to check feature access: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to check feature access: {e}"
                )
        
        @self.router.get("/upgrade-simulation/{user_id}", response_model=UpgradeSimulationResponse)
        async def simulate_tier_upgrade(
            user_id: str,
            target_tier: str = Query(..., description="Target subscription tier")
        ):
            """
            Simulate what would be available with a tier upgrade
            
            Args:
                user_id: User identifier
                target_tier: Target subscription tier
                
            Returns:
                Upgrade simulation with benefits and improvements
            """
            try:
                self.log_info(f"Simulating tier upgrade for user: {user_id} to {target_tier}")
                
                # Validate target tier
                try:
                    target_tier_enum = SubscriptionTier(target_tier)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid target tier: {target_tier}"
                    )
                
                upgrade_simulation = await access_control_manager.upgrade_tier_simulation(
                    user_id=user_id,
                    target_tier=target_tier_enum
                )
                
                if "error" in upgrade_simulation:
                    raise HTTPException(
                        status_code=404,
                        detail=upgrade_simulation["error"]
                    )
                
                return UpgradeSimulationResponse(**upgrade_simulation)
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to simulate tier upgrade: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to simulate tier upgrade: {e}"
                )
        
        @self.router.get("/tiers")
        async def get_all_tiers():
            """
            Get information about all available subscription tiers
            
            Returns:
                Complete tier information and feature comparison
            """
            try:
                self.log_info("Getting all tier information")
                
                tiers_info = {}
                
                for tier in SubscriptionTier:
                    tier_limits = access_control_manager.tier_limits[tier]
                    
                    tiers_info[tier.value] = {
                        "tier": tier.value,
                        "monthly_browsing_tasks": tier_limits.monthly_browsing_tasks,
                        "concurrent_sessions": tier_limits.concurrent_sessions,
                        "max_workflow_complexity": tier_limits.max_workflow_complexity,
                        "storage_mb": tier_limits.storage_mb,
                        "verification_requests": tier_limits.verification_requests,
                        "custom_workflows": tier_limits.custom_workflows,
                        "api_calls_per_hour": tier_limits.api_calls_per_hour,
                        "features": [feature.value for feature in tier_limits.features],
                        "priority_level": tier_limits.priority_level,
                        "support_level": tier_limits.support_level
                    }
                
                return {
                    "tiers": tiers_info,
                    "feature_comparison": await self._generate_feature_comparison()
                }
                
            except Exception as e:
                self.log_error(f"Failed to get tier information: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve tier information: {e}"
                )
        
        @self.router.get("/analytics")
        async def get_quota_analytics(
            time_period: str = Query("24h", description="Time period for analytics"),
            user_id: Optional[str] = Query(None, description="Specific user to analyze")
        ):
            """
            Get quota and access control analytics
            
            Args:
                time_period: Time period for analysis (24h, 7d, 30d)
                user_id: Optional specific user to analyze
                
            Returns:
                Comprehensive analytics data
            """
            try:
                self.log_info(f"Getting quota analytics for period: {time_period}")
                
                analytics = await access_control_manager.get_access_analytics(
                    time_period=time_period,
                    user_id=user_id
                )
                
                if "error" in analytics:
                    raise HTTPException(
                        status_code=500,
                        detail=analytics["error"]
                    )
                
                return {
                    "time_period": time_period,
                    "user_id": user_id,
                    "analytics": analytics,
                    "generated_at": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get quota analytics: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve quota analytics: {e}"
                )
        
        @self.router.get("/upgrade-opportunities")
        async def get_upgrade_opportunities():
            """
            Get users who might benefit from tier upgrades
            
            Returns:
                List of upgrade opportunities with recommendations
            """
            try:
                self.log_info("Getting upgrade opportunities")
                
                analytics = await access_control_manager.get_access_analytics()
                upgrade_opportunities = analytics.get("upgrade_opportunities", [])
                
                return {
                    "opportunities": upgrade_opportunities,
                    "total_opportunities": len(upgrade_opportunities),
                    "generated_at": datetime.now().isoformat()
                }
                
            except Exception as e:
                self.log_error(f"Failed to get upgrade opportunities: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve upgrade opportunities: {e}"
                )
        
        @self.router.post("/consume-quota/{user_id}")
        async def consume_user_quota(
            user_id: str,
            quota_type: str,
            amount: int = 1,
            metadata: Optional[Dict[str, Any]] = None
        ):
            """
            Consume user quota for testing or manual adjustments
            
            Args:
                user_id: User identifier
                quota_type: Type of quota to consume
                amount: Amount to consume
                metadata: Optional metadata about the consumption
                
            Returns:
                Quota consumption result
            """
            try:
                self.log_info(f"Consuming quota for user: {user_id}, type: {quota_type}, amount: {amount}")
                
                # Validate quota type
                from app.services.access_control import QuotaType
                try:
                    quota_type_enum = QuotaType(quota_type)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid quota type: {quota_type}"
                    )
                
                consumption_result = await access_control_manager.consume_quota(
                    user_id=user_id,
                    quota_type=quota_type_enum,
                    amount=amount,
                    metadata=metadata or {"source": "manual_api"}
                )
                
                return {
                    "user_id": user_id,
                    "quota_type": quota_type,
                    "amount_requested": amount,
                    "consumption_result": consumption_result,
                    "timestamp": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to consume quota: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to consume quota: {e}"
                )
    
    async def _generate_feature_comparison(self) -> Dict[str, Dict[str, bool]]:
        """Generate feature comparison matrix across tiers"""
        try:
            comparison = {}
            
            all_features = set()
            for tier_limits in access_control_manager.tier_limits.values():
                all_features.update(tier_limits.features)
            
            for feature in all_features:
                comparison[feature.value] = {}
                
                for tier in SubscriptionTier:
                    tier_limits = access_control_manager.tier_limits[tier]
                    comparison[feature.value][tier.value] = feature in tier_limits.features
            
            return comparison
            
        except Exception as e:
            self.log_error(f"Failed to generate feature comparison: {e}")
            return {}


# Create router instance
quota_dashboard_api = QuotaDashboardAPI()
router = quota_dashboard_api.router
