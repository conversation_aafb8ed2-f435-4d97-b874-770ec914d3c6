"""
Browser automation API endpoints
"""

import async<PERSON>
import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Request
from fastapi.responses import StreamingResponse

from app.models.browser_automation import (
    BrowserAutomationRequest,
    BrowserAutomationResponse,
    BrowserAutomationState,
    TaskStatus
)
from app.services.browser_orchestrator import BrowserOrchestrator
from app.services.session_manager import SessionManager
from app.core.logging import get_logger
from app.core.exceptions import BrowserAutomationException, TierLimitException

logger = get_logger(__name__)
router = APIRouter()


async def get_session_manager(request: Request) -> SessionManager:
    """Dependency to get session manager"""
    return request.app.state.session_manager


@router.post("/execute", response_model=BrowserAutomationResponse)
async def execute_browser_task(
    request: BrowserAutomationRequest,
    background_tasks: BackgroundTasks,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Execute a browser automation task with LangGraph orchestration

    This endpoint:
    1. Validates user tier and quotas
    2. Creates a LangGraph workflow with hierarchical agents
    3. Executes browser automation with Browser Use
    4. Returns comprehensive results with todo tracking
    """
    task_id = str(uuid.uuid4())

    logger.info(
        "Starting browser automation task",
        task_id=task_id,
        user_id=request.user_id,
        task=request.task[:100] + "..." if len(request.task) > 100 else request.task,
        task_type=getattr(request, 'task_type', 'navigation'),
        user_tier=getattr(request, 'user_tier', 'unknown')
    )
    
    try:
        # Validate user tier and quotas
        await _validate_user_access(request, session_manager)
        
        # Create browser orchestrator
        orchestrator = BrowserOrchestrator(
            task_id=task_id,
            request=request,
            session_manager=session_manager
        )
        
        # Execute the automation task
        result = await orchestrator.execute()
        
        # Update usage tracking in background
        background_tasks.add_task(
            _update_usage_tracking,
            request.user_id,
            request.user_tier,
            result.execution_metadata
        )
        
        logger.info(
            "Browser automation task completed",
            task_id=task_id,
            success=result.success,
            steps_completed=result.execution_metadata.steps_completed
        )
        
        return result
        
    except TierLimitException as e:
        logger.warning(
            "Tier limit exceeded",
            task_id=task_id,
            user_id=request.user_id,
            error=str(e)
        )
        raise HTTPException(status_code=429, detail=str(e))
        
    except BrowserAutomationException as e:
        logger.error(
            "Browser automation error",
            task_id=task_id,
            error=str(e),
            error_code=e.error_code
        )
        raise HTTPException(status_code=400, detail=str(e))
        
    except Exception as e:
        logger.error(
            "Unexpected error in browser automation",
            task_id=task_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/execute/stream")
async def execute_browser_task_stream(
    request: BrowserAutomationRequest,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Execute a browser automation task with streaming progress updates
    """
    task_id = str(uuid.uuid4())
    
    logger.info(
        "Starting streaming browser automation task",
        task_id=task_id,
        user_id=request.user_id
    )
    
    async def generate_stream():
        try:
            # Validate user access
            await _validate_user_access(request, session_manager)
            
            # Create orchestrator with streaming
            orchestrator = BrowserOrchestrator(
                task_id=task_id,
                request=request,
                session_manager=session_manager,
                enable_streaming=True
            )
            
            # Stream execution progress
            async for update in orchestrator.execute_stream():
                yield f"data: {update.json()}\n\n"
                
        except Exception as e:
            error_data = {
                "error": True,
                "message": str(e),
                "task_id": task_id
            }
            yield f"data: {error_data}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Get the current status of a browser automation task"""
    try:
        status = await session_manager.get_task_status(task_id)
        if not status:
            raise HTTPException(status_code=404, detail="Task not found")
        return status
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/task/{task_id}")
async def cancel_task(
    task_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """Cancel a running browser automation task"""
    try:
        success = await session_manager.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found or already completed")
        return {"message": "Task cancelled successfully", "task_id": task_id}
    except Exception as e:
        logger.error(f"Error cancelling task: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _validate_user_access(request: BrowserAutomationRequest, session_manager: SessionManager):
    """Validate user access using real RouKey system"""

    from app.services.rokey_integration import RouKeyIntegration

    rokey_integration = RouKeyIntegration()

    try:
        # Get user's actual subscription tier from RouKey
        user_subscription = await rokey_integration.get_user_subscription(request.user_id)

        if not user_subscription:
            raise TierLimitException("User subscription not found")

        actual_tier = user_subscription.get("tier", "free")

        # RouKey tier limits - Professional and Enterprise have unlimited browsing
        if actual_tier == "free":
            raise TierLimitException("Browser automation not available for free tier")
        elif actual_tier == "starter":
            monthly_limit = 15
        elif actual_tier in ["professional", "enterprise"]:
            monthly_limit = -1  # Unlimited
        else:
            raise TierLimitException(f"Unknown tier: {actual_tier}")

        # Check monthly usage only if not unlimited
        if monthly_limit > 0:
            monthly_usage = await rokey_integration.get_user_monthly_browser_usage(request.user_id)
            if monthly_usage >= monthly_limit:
                raise TierLimitException(f"Monthly browser automation limit reached ({monthly_limit})")

        # Update request with actual tier
        request.user_tier = actual_tier

        logger.info(
            "User access validated",
            user_id=request.user_id,
            tier=actual_tier,
            monthly_limit=monthly_limit,
            browser_automation_enabled=True
        )

    except Exception as e:
        logger.error(f"Failed to validate user access: {e}")
        raise TierLimitException(f"Access validation failed: {str(e)}")


async def _update_usage_tracking(user_id: str, user_tier: str, metadata: Any):
    """Update usage tracking in background"""
    try:
        # TODO: Implement usage tracking to RouKey database
        logger.info(
            "Usage tracking updated",
            user_id=user_id,
            user_tier=user_tier,
            tokens_used=metadata.tokens_used,
            steps_completed=metadata.steps_completed
        )
    except Exception as e:
        logger.error(f"Error updating usage tracking: {e}")
