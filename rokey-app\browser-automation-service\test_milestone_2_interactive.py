#!/usr/bin/env python3
"""
Interactive Testing for Milestone 2: Browser Automation
Allows real-time monitoring and step-by-step testing
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any, Optional

# Real Pro User Data
USER_ID = "69d967d5-0b7b-402b-ae1b-711d9b74eef4"
CONFIG_ID = "b39270c0-feb8-4c99-b6d2-9ee224edd57e"
USER_TIER = "professional"

class InteractiveTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=600.0)
        
    async def test_messi_task(self):
        """Test the specific Messi task mentioned by user"""
        print("🏈 Testing Messi Football Information Task")
        print("=" * 50)
        
        task = "when does messi play his next match and did he score in his last?"
        
        request_data = {
            "user_id": USER_ID,
            "config_id": CONFIG_ID,
            "user_tier": USER_TIER,
            "task": task,
            "workflow_type": "hierarchical",
            "routing_strategy": "intelligent_role",
            "max_roles": 3,
            "max_steps": 15,
            "timeout_seconds": 300,
            "enable_memory": True,
            "enable_screenshots": True,
            "headless": True,
            "verify_results": True,
            "api_keys": [
                {
                    "provider": "google",
                    "model": "google/gemini-2.0-flash-001",
                    "api_key": "real_key_from_user_config"
                },
                {
                    "provider": "openrouter", 
                    "model": "microsoft/phi-4-reasoning:free",
                    "api_key": "real_key_from_user_config"
                }
            ]
        }
        
        print(f"📝 Task: {task}")
        print("🔄 Executing...")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/browser/execute",
                json=request_data
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                await self._display_detailed_result(result, execution_time)
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
    async def test_simple_task(self):
        """Test a simpler task for quick validation"""
        print("🌤️ Testing Simple Weather Task")
        print("=" * 50)
        
        task = "what's the weather like in New York today?"
        
        request_data = {
            "user_id": USER_ID,
            "config_id": CONFIG_ID,
            "user_tier": USER_TIER,
            "task": task,
            "workflow_type": "sequential",
            "routing_strategy": "intelligent_role",
            "max_roles": 2,
            "max_steps": 8,
            "timeout_seconds": 180,
            "enable_memory": True,
            "enable_screenshots": False,
            "headless": True,
            "verify_results": True
        }
        
        print(f"📝 Task: {task}")
        print("🔄 Executing...")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/browser/execute",
                json=request_data
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                await self._display_detailed_result(result, execution_time)
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
    async def test_service_components(self):
        """Test individual service components"""
        print("🔧 Testing Service Components")
        print("=" * 50)
        
        # Test health
        print("1. Testing Health Endpoints...")
        try:
            response = await self.client.get(f"{self.base_url}/health/ready")
            print(f"   Health: {'✅ OK' if response.status_code == 200 else '❌ FAIL'}")
        except Exception as e:
            print(f"   Health: ❌ ERROR - {e}")
            
        # Test detailed health
        print("2. Testing Detailed Health...")
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/test/health-detailed")
            if response.status_code == 200:
                health = response.json()
                print(f"   Browser Use: {'✅' if health.get('browser_use_available') else '❌'}")
                print(f"   LangGraph: {'✅' if health.get('langgraph_available') else '❌'}")
                print(f"   Playwright: {'✅' if health.get('playwright_available') else '❌'}")
            else:
                print(f"   Detailed Health: ❌ FAIL ({response.status_code})")
        except Exception as e:
            print(f"   Detailed Health: ❌ ERROR - {e}")
            
        # Test user config endpoint
        print("3. Testing User Config...")
        try:
            response = await self.client.get(f"{self.base_url}/api/user-config/{USER_ID}")
            print(f"   User Config: {'✅ OK' if response.status_code == 200 else '❌ FAIL'}")
        except Exception as e:
            print(f"   User Config: ❌ ERROR - {e}")
            
    async def _display_detailed_result(self, result: Dict[str, Any], execution_time: float):
        """Display detailed results with formatting"""
        print("\n📊 EXECUTION RESULTS")
        print("-" * 30)
        
        success = result.get("success", False)
        task_id = result.get("task_id", "unknown")
        
        print(f"✅ Success: {success}")
        print(f"🆔 Task ID: {task_id}")
        print(f"⏱️ Execution Time: {execution_time:.2f}s")
        
        # Metadata analysis
        metadata = result.get("execution_metadata", {})
        if metadata:
            print(f"\n📈 EXECUTION METADATA")
            print(f"   Steps completed: {metadata.get('steps_completed', 0)}")
            print(f"   Roles used: {', '.join(metadata.get('roles_used', []))}")
            print(f"   Pages visited: {len(metadata.get('pages_visited', []))}")
            print(f"   Actions performed: {', '.join(metadata.get('actions_performed', []))}")
            
            # Show pages visited
            pages = metadata.get('pages_visited', [])
            if pages:
                print(f"\n🌐 PAGES VISITED:")
                for i, page in enumerate(pages[:5], 1):  # Show first 5
                    print(f"   {i}. {page.get('url', 'Unknown URL')}")
                if len(pages) > 5:
                    print(f"   ... and {len(pages) - 5} more")
                    
        # Show result
        final_result = result.get("result", "")
        print(f"\n📋 FINAL RESULT:")
        if len(final_result) > 500:
            print(f"{final_result[:500]}...")
            print(f"\n[Result truncated - Total length: {len(final_result)} characters]")
        else:
            print(final_result)
            
        # Show any errors
        errors = result.get("errors", [])
        if errors:
            print(f"\n⚠️ ERRORS ENCOUNTERED:")
            for error in errors:
                print(f"   - {error}")
                
        print("\n" + "=" * 50)
        
    async def monitor_task_status(self, task_id: str):
        """Monitor a running task's status"""
        print(f"👀 Monitoring task: {task_id}")
        
        while True:
            try:
                response = await self.client.get(f"{self.base_url}/api/v1/browser/task/{task_id}/status")
                if response.status_code == 200:
                    status = response.json()
                    print(f"Status: {status.get('status', 'unknown')} - Steps: {status.get('steps_completed', 0)}")
                    
                    if status.get('status') in ['completed', 'failed', 'cancelled']:
                        break
                else:
                    print(f"Failed to get status: {response.status_code}")
                    break
                    
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                print(f"Error monitoring task: {e}")
                break
                
    async def run_interactive_menu(self):
        """Run interactive testing menu"""
        while True:
            print("\n🧪 MILESTONE 2 INTERACTIVE TESTER")
            print("=" * 40)
            print("1. Test Messi Football Task (Complex)")
            print("2. Test Weather Task (Simple)")
            print("3. Test Service Components")
            print("4. Custom Task")
            print("5. Exit")
            print("-" * 40)
            
            choice = input("Select option (1-5): ").strip()
            
            if choice == "1":
                await self.test_messi_task()
            elif choice == "2":
                await self.test_simple_task()
            elif choice == "3":
                await self.test_service_components()
            elif choice == "4":
                await self.test_custom_task()
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
                
    async def test_custom_task(self):
        """Test a custom user-defined task"""
        print("🎯 Custom Task Testing")
        print("=" * 30)
        
        task = input("Enter your task: ").strip()
        if not task:
            print("❌ No task entered.")
            return
            
        complexity = input("Complexity (simple/medium/complex) [medium]: ").strip() or "medium"
        
        # Set parameters based on complexity
        if complexity == "simple":
            max_steps, timeout, max_roles = 5, 120, 1
        elif complexity == "complex":
            max_steps, timeout, max_roles = 20, 400, 3
        else:  # medium
            max_steps, timeout, max_roles = 10, 240, 2
            
        request_data = {
            "user_id": USER_ID,
            "config_id": CONFIG_ID,
            "user_tier": USER_TIER,
            "task": task,
            "workflow_type": "hierarchical" if complexity == "complex" else "sequential",
            "routing_strategy": "intelligent_role",
            "max_roles": max_roles,
            "max_steps": max_steps,
            "timeout_seconds": timeout,
            "enable_memory": True,
            "enable_screenshots": complexity == "complex",
            "headless": True,
            "verify_results": True
        }
        
        print(f"📝 Task: {task}")
        print(f"🎯 Complexity: {complexity}")
        print(f"⏱️ Timeout: {timeout}s")
        print("🔄 Executing...")
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/browser/execute",
                json=request_data
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                await self._display_detailed_result(result, execution_time)
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main execution"""
    tester = InteractiveTester()
    
    try:
        await tester.run_interactive_menu()
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
