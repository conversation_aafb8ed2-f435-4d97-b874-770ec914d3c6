/**
 * Frontend Integration Example
 * Shows how browser automation configuration integrates with RouKey's custom model setup UI
 */

// Browser Automation Configuration Component for RouKey Custom Model Setup
class BrowserAutomationConfig {
    constructor(userId, apiBaseUrl = '/api/user-config') {
        this.userId = userId;
        this.apiBaseUrl = apiBaseUrl;
        this.config = null;
        this.uiData = null;
        
        this.init();
    }
    
    async init() {
        await this.loadConfigData();
        this.renderConfigUI();
        this.setupEventListeners();
    }
    
    async loadConfigData() {
        try {
            // Load UI configuration data
            const response = await fetch(`${this.apiBaseUrl}/ui-data/${this.userId}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || 'Failed to load configuration');
            }
            
            this.uiData = data.config_data;
            
            // Load current user configuration
            const configResponse = await fetch(`${this.apiBaseUrl}/config/${this.userId}`);
            if (configResponse.ok) {
                this.config = await configResponse.json();
            }
            
        } catch (error) {
            console.error('Failed to load config data:', error);
            this.showError('Failed to load configuration data');
        }
    }
    
    renderConfigUI() {
        const container = document.getElementById('browser-automation-config');
        if (!container) return;
        
        const browserAutomation = this.uiData?.browser_automation || {};
        const currentStatus = browserAutomation.current_status || {};
        const tierInfo = browserAutomation.tier_info || {};
        const features = browserAutomation.features || {};
        
        container.innerHTML = `
            <div class="config-section">
                <div class="section-header">
                    <h3>🌐 Browser Automation</h3>
                    <span class="tier-badge tier-${tierInfo.current_tier}">${tierInfo.current_tier?.toUpperCase()}</span>
                </div>
                
                ${this.renderAvailabilityStatus(browserAutomation, tierInfo)}
                ${this.renderMainToggle(currentStatus, browserAutomation.available)}
                ${this.renderFeatureSettings(features, currentStatus.enabled)}
                ${this.renderTemplates(browserAutomation.templates)}
                ${this.renderUsageInfo(browserAutomation.usage)}
                ${this.renderAdvancedSettings(features, currentStatus.enabled)}
            </div>
        `;
    }
    
    renderAvailabilityStatus(browserAutomation, tierInfo) {
        if (!browserAutomation.available) {
            return `
                <div class="availability-notice upgrade-required">
                    <div class="notice-content">
                        <h4>🚀 Upgrade Required</h4>
                        <p>Browser automation is available with Pro and Enterprise plans.</p>
                        <div class="upgrade-benefits">
                            <ul>
                                <li>✅ Automated web navigation and data extraction</li>
                                <li>✅ Intelligent role-based task routing</li>
                                <li>✅ Advanced verification and error handling</li>
                                <li>✅ Performance optimization and session management</li>
                            </ul>
                        </div>
                        <button class="btn btn-primary upgrade-btn" onclick="window.open('/pricing', '_blank')">
                            Upgrade to ${tierInfo.required_tier?.toUpperCase()} Plan
                        </button>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="availability-notice available">
                <div class="notice-content">
                    <h4>✅ Browser Automation Available</h4>
                    <p>Configure automated web browsing and data extraction for your custom models.</p>
                </div>
            </div>
        `;
    }
    
    renderMainToggle(currentStatus, available) {
        if (!available) return '';
        
        return `
            <div class="main-toggle-section">
                <div class="toggle-container">
                    <label class="toggle-switch">
                        <input type="checkbox" id="browser-automation-enabled" 
                               ${currentStatus.enabled ? 'checked' : ''}>
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="toggle-label">
                        <h4>Enable Browser Automation</h4>
                        <p>Allow your custom models to automatically browse websites and extract data</p>
                    </div>
                </div>
                
                <div class="status-indicator ${currentStatus.enabled ? 'enabled' : 'disabled'}">
                    <span class="status-dot"></span>
                    <span class="status-text">
                        ${currentStatus.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </div>
            </div>
        `;
    }
    
    renderFeatureSettings(features, enabled) {
        if (!enabled) return '';
        
        return `
            <div class="feature-settings">
                <h4>🔧 Feature Configuration</h4>
                
                <div class="feature-grid">
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" checked disabled>
                            <span class="checkmark"></span>
                            Web Navigation
                        </label>
                        <span class="feature-badge included">Included</span>
                    </div>
                    
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" checked disabled>
                            <span class="checkmark"></span>
                            Data Extraction
                        </label>
                        <span class="feature-badge included">Included</span>
                    </div>
                    
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" id="form-interaction" 
                                   ${features.form_interaction ? '' : 'disabled'}>
                            <span class="checkmark"></span>
                            Form Interaction
                        </label>
                        <span class="feature-badge ${features.form_interaction ? 'available' : 'tier-restricted'}">
                            ${features.form_interaction ? 'Available' : 'Pro+'}
                        </span>
                    </div>
                    
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" id="file-downloads" 
                                   ${features.file_downloads ? '' : 'disabled'}>
                            <span class="checkmark"></span>
                            File Downloads
                        </label>
                        <span class="feature-badge ${features.file_downloads ? 'available' : 'tier-restricted'}">
                            ${features.file_downloads ? 'Available' : 'Pro+'}
                        </span>
                    </div>
                    
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" checked disabled>
                            <span class="checkmark"></span>
                            Screenshot Capture
                        </label>
                        <span class="feature-badge included">Included</span>
                    </div>
                    
                    <div class="feature-item">
                        <label class="feature-toggle">
                            <input type="checkbox" checked disabled>
                            <span class="checkmark"></span>
                            Result Verification
                        </label>
                        <span class="feature-badge included">Included</span>
                    </div>
                </div>
                
                <div class="performance-settings">
                    <h5>⚡ Performance Settings</h5>
                    <div class="setting-row">
                        <label>Concurrent Sessions:</label>
                        <select id="max-sessions">
                            ${Array.from({length: features.max_concurrent_sessions}, (_, i) => 
                                `<option value="${i + 1}">${i + 1}</option>`
                            ).join('')}
                        </select>
                        <span class="setting-note">Max: ${features.max_concurrent_sessions}</span>
                    </div>
                    
                    <div class="setting-row">
                        <label>Session Timeout:</label>
                        <select id="session-timeout">
                            <option value="5">5 minutes</option>
                            <option value="10" selected>10 minutes</option>
                            <option value="15">15 minutes</option>
                            <option value="30">30 minutes</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderTemplates(templates) {
        if (!templates || Object.keys(templates).length === 0) return '';
        
        return `
            <div class="template-section">
                <h4>📋 Quick Setup Templates</h4>
                <p>Choose a pre-configured template to get started quickly</p>
                
                <div class="template-grid">
                    ${Object.entries(templates).map(([key, template]) => `
                        <div class="template-card" data-template="${key}">
                            <div class="template-header">
                                <h5>${template.name}</h5>
                                <span class="template-mode">${template.mode}</span>
                            </div>
                            <p class="template-description">${template.description}</p>
                            <div class="template-features">
                                ${template.features.slice(0, 3).map(feature => 
                                    `<span class="feature-tag">${feature}</span>`
                                ).join('')}
                                ${template.features.length > 3 ? 
                                    `<span class="feature-tag more">+${template.features.length - 3} more</span>` : ''
                                }
                            </div>
                            <button class="btn btn-outline apply-template-btn" data-template="${key}">
                                Apply Template
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    renderUsageInfo(usage) {
        if (!usage) return '';
        
        const usagePercent = usage.monthly_limit > 0 ? 
            (usage.current_usage / usage.monthly_limit) * 100 : 0;
        
        return `
            <div class="usage-section">
                <h4>📊 Usage Information</h4>
                
                <div class="usage-stats">
                    <div class="usage-bar">
                        <div class="usage-progress" style="width: ${usagePercent}%"></div>
                    </div>
                    <div class="usage-text">
                        <span>${usage.current_usage || 0} / ${usage.monthly_limit || 0} tasks this month</span>
                        <span class="usage-remaining">${usage.remaining || 0} remaining</span>
                    </div>
                </div>
                
                ${usage.reset_date ? `
                    <p class="usage-reset">Usage resets on ${new Date(usage.reset_date).toLocaleDateString()}</p>
                ` : ''}
            </div>
        `;
    }
    
    renderAdvancedSettings(features, enabled) {
        if (!enabled || !features.advanced_features) return '';
        
        return `
            <div class="advanced-settings collapsed">
                <div class="advanced-header" onclick="this.parentElement.classList.toggle('collapsed')">
                    <h4>⚙️ Advanced Settings</h4>
                    <span class="expand-icon">▼</span>
                </div>
                
                <div class="advanced-content">
                    <div class="setting-group">
                        <h5>Security & Access</h5>
                        
                        <div class="setting-row">
                            <label>
                                <input type="checkbox" id="allow-external-sites" checked>
                                Allow External Sites
                            </label>
                        </div>
                        
                        <div class="setting-row">
                            <label>Blocked Domains:</label>
                            <textarea id="blocked-domains" placeholder="Enter domains to block, one per line"></textarea>
                        </div>
                        
                        <div class="setting-row">
                            <label>Allowed Domains:</label>
                            <textarea id="allowed-domains" placeholder="Enter allowed domains, one per line"></textarea>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h5>Integration</h5>
                        
                        <div class="setting-row">
                            <label>
                                <input type="checkbox" id="enable-role-routing" checked>
                                Enable Role-based Routing
                            </label>
                        </div>
                        
                        <div class="setting-row">
                            <label>Automation Priority:</label>
                            <input type="range" id="automation-priority" min="1" max="10" value="5">
                            <span class="range-value">5</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        // Main toggle
        const mainToggle = document.getElementById('browser-automation-enabled');
        if (mainToggle) {
            mainToggle.addEventListener('change', (e) => {
                this.toggleBrowserAutomation(e.target.checked);
            });
        }
        
        // Template application
        document.querySelectorAll('.apply-template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.dataset.template;
                this.applyTemplate(template);
            });
        });
        
        // Advanced settings toggle
        const priorityRange = document.getElementById('automation-priority');
        if (priorityRange) {
            priorityRange.addEventListener('input', (e) => {
                const valueSpan = e.target.nextElementSibling;
                if (valueSpan) {
                    valueSpan.textContent = e.target.value;
                }
            });
        }
        
        // Save configuration button
        this.addSaveButton();
    }
    
    async toggleBrowserAutomation(enabled) {
        try {
            this.showLoading('Updating browser automation settings...');
            
            const response = await fetch(`${this.apiBaseUrl}/toggle-automation/${this.userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    enabled: enabled,
                    config_name: 'default'
                })
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                if (response.status === 403 && result.required_tier) {
                    this.showUpgradeRequired(result.required_tier);
                    // Revert toggle
                    document.getElementById('browser-automation-enabled').checked = false;
                    return;
                }
                throw new Error(result.detail || 'Toggle failed');
            }
            
            this.showSuccess('Browser automation settings updated successfully');
            
            // Refresh UI to show/hide relevant sections
            setTimeout(() => {
                this.loadConfigData();
            }, 1000);
            
        } catch (error) {
            console.error('Toggle failed:', error);
            this.showError('Failed to update browser automation settings');
            
            // Revert toggle
            document.getElementById('browser-automation-enabled').checked = !enabled;
        } finally {
            this.hideLoading();
        }
    }
    
    async applyTemplate(templateName) {
        try {
            this.showLoading(`Applying ${templateName} template...`);
            
            const response = await fetch(`${this.apiBaseUrl}/apply-template/${this.userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    template_name: templateName,
                    config_name: 'default'
                })
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.detail || 'Template application failed');
            }
            
            this.showSuccess(`${templateName.charAt(0).toUpperCase() + templateName.slice(1)} template applied successfully`);
            
            // Refresh configuration
            setTimeout(() => {
                this.loadConfigData();
            }, 1000);
            
        } catch (error) {
            console.error('Template application failed:', error);
            this.showError('Failed to apply template');
        } finally {
            this.hideLoading();
        }
    }
    
    addSaveButton() {
        const container = document.getElementById('browser-automation-config');
        if (!container) return;
        
        const saveButton = document.createElement('button');
        saveButton.className = 'btn btn-primary save-config-btn';
        saveButton.textContent = 'Save Configuration';
        saveButton.onclick = () => this.saveConfiguration();
        
        container.appendChild(saveButton);
    }
    
    async saveConfiguration() {
        try {
            this.showLoading('Saving configuration...');
            
            // Collect all form data
            const configData = this.collectFormData();
            
            const response = await fetch(`${this.apiBaseUrl}/config/${this.userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData)
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.detail || 'Save failed');
            }
            
            this.showSuccess('Configuration saved successfully');
            
        } catch (error) {
            console.error('Save failed:', error);
            this.showError('Failed to save configuration');
        } finally {
            this.hideLoading();
        }
    }
    
    collectFormData() {
        // Collect form data from all inputs
        return {
            config_name: 'default',
            browser_automation: {
                enabled: document.getElementById('browser-automation-enabled')?.checked || false,
                enable_form_interaction: document.getElementById('form-interaction')?.checked || false,
                enable_file_downloads: document.getElementById('file-downloads')?.checked || false,
                max_concurrent_sessions: parseInt(document.getElementById('max-sessions')?.value || '1'),
                session_timeout_minutes: parseInt(document.getElementById('session-timeout')?.value || '10'),
                allow_external_sites: document.getElementById('allow-external-sites')?.checked || true,
                blocked_domains: document.getElementById('blocked-domains')?.value.split('\n').filter(d => d.trim()) || [],
                allowed_domains: document.getElementById('allowed-domains')?.value.split('\n').filter(d => d.trim()) || [],
                enable_role_routing: document.getElementById('enable-role-routing')?.checked || true
            },
            browser_automation_priority: parseInt(document.getElementById('automation-priority')?.value || '5')
        };
    }
    
    // UI Helper Methods
    showLoading(message) {
        // Implementation for loading state
        console.log('Loading:', message);
    }
    
    hideLoading() {
        // Implementation for hiding loading state
        console.log('Loading complete');
    }
    
    showSuccess(message) {
        // Implementation for success notification
        console.log('Success:', message);
    }
    
    showError(message) {
        // Implementation for error notification
        console.error('Error:', message);
    }
    
    showUpgradeRequired(requiredTier) {
        // Implementation for upgrade required notification
        console.log('Upgrade required to:', requiredTier);
    }
}

// Initialize browser automation config when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // This would be integrated into RouKey's existing custom model setup page
    const userId = getCurrentUserId(); // RouKey's existing function
    
    if (userId && document.getElementById('browser-automation-config')) {
        new BrowserAutomationConfig(userId);
    }
});

// Example CSS styles (would be integrated into RouKey's existing styles)
const styles = `
.config-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tier-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.tier-starter { background: #e3f2fd; color: #1976d2; }
.tier-pro { background: #f3e5f5; color: #7b1fa2; }
.tier-enterprise { background: #e8f5e8; color: #388e3c; }

.availability-notice {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.availability-notice.available {
    background: #e8f5e8;
    border: 1px solid #4caf50;
}

.availability-notice.upgrade-required {
    background: #fff3e0;
    border: 1px solid #ff9800;
}

.main-toggle-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 24px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-right: 16px;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #ff6b35;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.feature-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

.feature-badge.included { background: #e8f5e8; color: #388e3c; }
.feature-badge.available { background: #e3f2fd; color: #1976d2; }
.feature-badge.tier-restricted { background: #fff3e0; color: #f57c00; }

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.template-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s;
}

.template-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.usage-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.usage-progress {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #ff9800);
    transition: width 0.3s;
}

.advanced-settings.collapsed .advanced-content {
    display: none;
}

.advanced-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.save-config-btn {
    width: 100%;
    margin-top: 24px;
    padding: 12px;
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
}
`;

// Add styles to document
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);
