"""
Chat Integration API
Endpoints for integrating browser automation with RouKey's existing chat API system
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import uuid
import json

from app.core.logging import LoggerMixin
from app.services.browser_orchestrator import BrowserOrchestrator
from app.services.access_control import access_control_manager, FeatureType
from app.api.browser_automation import browser_automation_api


class ChatBrowserRequest(BaseModel):
    """Request model for browser automation via chat"""
    user_id: str = Field(..., description="User identifier")
    conversation_id: str = Field(..., description="Chat conversation ID")
    message_id: str = Field(..., description="Chat message ID")
    user_message: str = Field(..., description="User's chat message")
    detected_intent: str = Field(..., description="Detected browser automation intent")
    extracted_parameters: Dict[str, Any] = Field(default_factory=dict, description="Extracted parameters")
    context: Dict[str, Any] = Field(default_factory=dict, description="Chat context")
    routing_strategy: str = Field("intelligent", description="RouKey routing strategy")
    roles: List[str] = Field(default_factory=list, description="Assigned roles for the task")


class ChatBrowserResponse(BaseModel):
    """Response model for chat browser automation"""
    conversation_id: str
    message_id: str
    task_id: str
    status: str
    response_message: str
    browsing_enabled: bool
    quota_status: Dict[str, Any]
    estimated_completion: Optional[str] = None
    streaming_url: Optional[str] = None


class BrowserCapabilityCheck(BaseModel):
    """Model for checking browser automation capabilities"""
    user_id: str
    requested_action: str
    context: Optional[Dict[str, Any]] = None


class ChatIntegrationAPI(LoggerMixin):
    """API endpoints for chat integration with browser automation"""
    
    def __init__(self):
        self.router = APIRouter(prefix="/api/chat-integration", tags=["chat-integration"])
        self.chat_tasks: Dict[str, Dict[str, Any]] = {}
        self._setup_routes()
        self.log_info("Chat integration API initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.router.post("/check-capability")
        async def check_browser_capability(request: BrowserCapabilityCheck):
            """
            Check if user can perform browser automation and what capabilities are available
            
            Args:
                request: Capability check request
                
            Returns:
                Capability information and access status
            """
            try:
                self.log_info(f"Checking browser capability for user: {request.user_id}")
                
                # Check basic browsing access
                browsing_access = await access_control_manager.check_access(
                    user_id=request.user_id,
                    feature=FeatureType.BASIC_BROWSING
                )
                
                # Get quota status
                quota_status = await access_control_manager.check_browsing_quota(request.user_id)
                
                # Get user limits
                user_limits = await access_control_manager.get_user_limits(request.user_id)
                
                # Determine available capabilities
                capabilities = await self._determine_user_capabilities(request.user_id, user_limits)
                
                # Check if specific action is supported
                action_supported = await self._check_action_support(
                    request.requested_action, 
                    capabilities,
                    request.context or {}
                )
                
                return {
                    "user_id": request.user_id,
                    "browsing_enabled": browsing_access["access_granted"],
                    "quota_status": quota_status,
                    "capabilities": capabilities,
                    "requested_action": request.requested_action,
                    "action_supported": action_supported,
                    "tier": user_limits.get("subscription_tier", "unknown"),
                    "upgrade_required": not browsing_access["access_granted"],
                    "upgrade_url": "/upgrade" if not browsing_access["access_granted"] else None
                }
                
            except Exception as e:
                self.log_error(f"Capability check failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to check browser capability: {e}"
                )
        
        @self.router.post("/execute-from-chat", response_model=ChatBrowserResponse)
        async def execute_browser_task_from_chat(
            request: ChatBrowserRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Execute browser automation task initiated from chat
            
            Args:
                request: Chat browser request
                background_tasks: FastAPI background tasks
                
            Returns:
                Chat browser response with task details
            """
            try:
                self.log_info(f"Chat browser task requested: {request.conversation_id}")
                
                # Check capability first
                capability_check = await self.check_browser_capability(
                    BrowserCapabilityCheck(
                        user_id=request.user_id,
                        requested_action=request.detected_intent,
                        context=request.context
                    )
                )
                
                if not capability_check["browsing_enabled"]:
                    return ChatBrowserResponse(
                        conversation_id=request.conversation_id,
                        message_id=request.message_id,
                        task_id="",
                        status="access_denied",
                        response_message=self._generate_upgrade_message(capability_check),
                        browsing_enabled=False,
                        quota_status=capability_check["quota_status"]
                    )
                
                if not capability_check["action_supported"]["supported"]:
                    return ChatBrowserResponse(
                        conversation_id=request.conversation_id,
                        message_id=request.message_id,
                        task_id="",
                        status="unsupported_action",
                        response_message=f"The requested action '{request.detected_intent}' is not supported in your current plan. {capability_check['action_supported']['reason']}",
                        browsing_enabled=True,
                        quota_status=capability_check["quota_status"]
                    )
                
                # Convert chat request to browser task
                browser_task = await self._convert_chat_to_browser_task(request)
                
                # Execute browser task
                task_response = await self._execute_chat_browser_task(browser_task, background_tasks)
                
                # Create chat task tracking
                chat_task_id = f"chat_{request.conversation_id}_{request.message_id}"
                self.chat_tasks[chat_task_id] = {
                    "chat_task_id": chat_task_id,
                    "conversation_id": request.conversation_id,
                    "message_id": request.message_id,
                    "browser_task_id": task_response.task_id,
                    "user_id": request.user_id,
                    "status": "pending",
                    "created_at": datetime.now(),
                    "request": request.model_dump()
                }
                
                # Generate response message
                response_message = await self._generate_task_response_message(
                    request.detected_intent,
                    task_response,
                    capability_check["quota_status"]
                )
                
                return ChatBrowserResponse(
                    conversation_id=request.conversation_id,
                    message_id=request.message_id,
                    task_id=task_response.task_id,
                    status="initiated",
                    response_message=response_message,
                    browsing_enabled=True,
                    quota_status=capability_check["quota_status"],
                    estimated_completion=self._estimate_completion_time(request.detected_intent),
                    streaming_url=f"/api/chat-integration/stream/{task_response.task_id}"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Chat browser task execution failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to execute browser task from chat: {e}"
                )
        
        @self.router.get("/stream/{task_id}")
        async def stream_task_progress(task_id: str):
            """
            Stream task progress for real-time updates in chat
            
            Args:
                task_id: Browser task identifier
                
            Returns:
                Server-sent events stream of task progress
            """
            try:
                from fastapi.responses import StreamingResponse
                
                async def generate_progress_stream():
                    """Generate progress updates as server-sent events"""
                    last_progress = 0
                    last_status = "pending"
                    
                    while True:
                        try:
                            # Get current task status
                            task_status = await browser_automation_api.get_task_status(task_id)
                            
                            current_progress = task_status.progress
                            current_status = task_status.status
                            
                            # Send update if progress or status changed
                            if current_progress != last_progress or current_status != last_status:
                                update = {
                                    "task_id": task_id,
                                    "status": current_status,
                                    "progress": current_progress,
                                    "timestamp": datetime.now().isoformat()
                                }
                                
                                if task_status.result:
                                    update["result"] = task_status.result
                                
                                if task_status.error:
                                    update["error"] = task_status.error
                                
                                yield f"data: {json.dumps(update)}\n\n"
                                
                                last_progress = current_progress
                                last_status = current_status
                            
                            # Stop streaming if task is complete
                            if current_status in ["completed", "failed", "cancelled"]:
                                break
                            
                            # Wait before next check
                            await asyncio.sleep(2)
                            
                        except Exception as e:
                            error_update = {
                                "task_id": task_id,
                                "status": "error",
                                "error": str(e),
                                "timestamp": datetime.now().isoformat()
                            }
                            yield f"data: {json.dumps(error_update)}\n\n"
                            break
                
                return StreamingResponse(
                    generate_progress_stream(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*"
                    }
                )
                
            except Exception as e:
                self.log_error(f"Task streaming failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to stream task progress: {e}"
                )
        
        @self.router.get("/chat-task/{conversation_id}/{message_id}")
        async def get_chat_task_status(conversation_id: str, message_id: str):
            """
            Get status of browser task initiated from chat
            
            Args:
                conversation_id: Chat conversation ID
                message_id: Chat message ID
                
            Returns:
                Chat task status and browser task details
            """
            try:
                chat_task_id = f"chat_{conversation_id}_{message_id}"
                
                if chat_task_id not in self.chat_tasks:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Chat task not found: {conversation_id}/{message_id}"
                    )
                
                chat_task = self.chat_tasks[chat_task_id]
                browser_task_id = chat_task["browser_task_id"]
                
                # Get browser task status
                browser_task_status = await browser_automation_api.get_task_status(browser_task_id)
                
                # Update chat task status
                chat_task["status"] = browser_task_status.status
                chat_task["updated_at"] = datetime.now()
                
                return {
                    "chat_task_id": chat_task_id,
                    "conversation_id": conversation_id,
                    "message_id": message_id,
                    "browser_task_id": browser_task_id,
                    "status": browser_task_status.status,
                    "progress": browser_task_status.progress,
                    "result": browser_task_status.result,
                    "error": browser_task_status.error,
                    "execution_time": browser_task_status.execution_time,
                    "created_at": chat_task["created_at"].isoformat(),
                    "updated_at": chat_task.get("updated_at", chat_task["created_at"]).isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get chat task status: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve chat task status: {e}"
                )
        
        @self.router.post("/intent-detection")
        async def detect_browser_intent(
            user_message: str,
            conversation_context: Optional[Dict[str, Any]] = None
        ):
            """
            Detect if user message requires browser automation
            
            Args:
                user_message: User's chat message
                conversation_context: Optional conversation context
                
            Returns:
                Intent detection results
            """
            try:
                self.log_info("Detecting browser automation intent")
                
                # Browser automation intent patterns
                browser_intents = {
                    "web_search": ["search for", "find information", "look up", "research"],
                    "data_extraction": ["extract data", "get information from", "scrape", "collect data"],
                    "navigation": ["go to", "visit", "navigate to", "open website"],
                    "verification": ["verify", "check if", "confirm", "validate"],
                    "form_filling": ["fill form", "submit", "enter data", "complete form"],
                    "monitoring": ["monitor", "track changes", "watch for", "alert when"],
                    "comparison": ["compare", "difference between", "which is better"],
                    "automation": ["automate", "schedule", "repeat", "do regularly"]
                }
                
                detected_intents = []
                confidence_scores = {}
                
                user_message_lower = user_message.lower()
                
                for intent, patterns in browser_intents.items():
                    matches = sum(1 for pattern in patterns if pattern in user_message_lower)
                    if matches > 0:
                        confidence = min(matches / len(patterns), 1.0)
                        detected_intents.append(intent)
                        confidence_scores[intent] = confidence
                
                # Determine primary intent
                primary_intent = None
                if detected_intents:
                    primary_intent = max(detected_intents, key=lambda x: confidence_scores[x])
                
                # Extract parameters based on intent
                extracted_parameters = await self._extract_intent_parameters(
                    user_message, primary_intent, conversation_context or {}
                )
                
                return {
                    "user_message": user_message,
                    "browser_automation_required": len(detected_intents) > 0,
                    "detected_intents": detected_intents,
                    "primary_intent": primary_intent,
                    "confidence_scores": confidence_scores,
                    "extracted_parameters": extracted_parameters,
                    "recommended_action": await self._recommend_action(primary_intent, extracted_parameters)
                }
                
            except Exception as e:
                self.log_error(f"Intent detection failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to detect browser intent: {e}"
                )

    # Helper methods for chat integration
    async def _determine_user_capabilities(self, user_id: str, user_limits: Dict[str, Any]) -> Dict[str, Any]:
        """Determine what browser automation capabilities user has access to"""
        try:
            if "error" in user_limits:
                return {"basic_browsing": False, "advanced_features": [], "limitations": ["User not found"]}

            available_features = user_limits.get("available_features", [])
            tier = user_limits.get("subscription_tier", "free")

            capabilities = {
                "basic_browsing": "basic_browsing" in available_features,
                "data_extraction": "advanced_extraction" in available_features,
                "verification": "verification" in available_features,
                "parallel_processing": "parallel_processing" in available_features,
                "custom_workflows": "custom_workflows" in available_features,
                "bulk_operations": "bulk_operations" in available_features,
                "tier": tier,
                "monthly_quota": user_limits.get("limits", {}).get("monthly_browsing_tasks", 0),
                "remaining_quota": user_limits.get("current_usage", {}).get("browsing_tasks", 0),
                "concurrent_sessions": user_limits.get("limits", {}).get("concurrent_sessions", 0),
                "limitations": []
            }

            # Add tier-specific limitations
            if tier == "free":
                capabilities["limitations"].append("No browser automation access")
            elif tier == "starter":
                capabilities["limitations"].extend([
                    "Limited to 15 tasks per month",
                    "Maximum 3 roles per workflow",
                    "Single concurrent session"
                ])
            elif tier == "pro":
                capabilities["limitations"].extend([
                    "Limited to 100 tasks per month",
                    "Maximum 10 roles per workflow"
                ])

            return capabilities

        except Exception as e:
            self.log_error(f"Failed to determine user capabilities: {e}")
            return {"basic_browsing": False, "advanced_features": [], "limitations": ["Error determining capabilities"]}

    async def _check_action_support(
        self,
        requested_action: str,
        capabilities: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check if specific action is supported by user's capabilities"""
        try:
            action_requirements = {
                "web_search": {"requires": ["basic_browsing"], "quota": 1},
                "data_extraction": {"requires": ["data_extraction"], "quota": 1},
                "navigation": {"requires": ["basic_browsing"], "quota": 1},
                "verification": {"requires": ["verification"], "quota": 1},
                "form_filling": {"requires": ["basic_browsing"], "quota": 1},
                "monitoring": {"requires": ["custom_workflows"], "quota": 2},
                "comparison": {"requires": ["data_extraction", "verification"], "quota": 2},
                "automation": {"requires": ["custom_workflows"], "quota": 3},
                "bulk_operations": {"requires": ["bulk_operations"], "quota": 5}
            }

            if requested_action not in action_requirements:
                return {
                    "supported": False,
                    "reason": f"Unknown action: {requested_action}",
                    "required_features": [],
                    "quota_needed": 0
                }

            requirements = action_requirements[requested_action]
            required_features = requirements["requires"]
            quota_needed = requirements["quota"]

            # Check feature requirements
            missing_features = []
            for feature in required_features:
                if not capabilities.get(feature, False):
                    missing_features.append(feature)

            # Check quota
            remaining_quota = capabilities.get("remaining_quota", 0)
            quota_sufficient = remaining_quota >= quota_needed

            supported = len(missing_features) == 0 and quota_sufficient

            reason = ""
            if missing_features:
                reason = f"Missing required features: {', '.join(missing_features)}"
            elif not quota_sufficient:
                reason = f"Insufficient quota. Need {quota_needed}, have {remaining_quota}"

            return {
                "supported": supported,
                "reason": reason,
                "required_features": required_features,
                "missing_features": missing_features,
                "quota_needed": quota_needed,
                "quota_sufficient": quota_sufficient
            }

        except Exception as e:
            self.log_error(f"Action support check failed: {e}")
            return {
                "supported": False,
                "reason": f"Error checking action support: {e}",
                "required_features": [],
                "quota_needed": 0
            }

    def _generate_upgrade_message(self, capability_check: Dict[str, Any]) -> str:
        """Generate upgrade message for users without access"""
        try:
            tier = capability_check.get("tier", "free")

            if tier == "free":
                return ("🚀 Browser automation is available with RouKey's paid plans! "
                       "Upgrade to Starter ($19/month) to get 15 browsing tasks per month, "
                       "data extraction, and verification capabilities. "
                       "Visit /upgrade to get started!")

            # For other tiers, suggest specific upgrades
            return ("⬆️ This feature requires a higher subscription tier. "
                   "Upgrade your plan to access advanced browser automation capabilities. "
                   "Visit /upgrade to see available options!")

        except Exception as e:
            self.log_error(f"Failed to generate upgrade message: {e}")
            return "Browser automation requires a paid subscription. Visit /upgrade for more information."

    async def _convert_chat_to_browser_task(self, request: ChatBrowserRequest):
        """Convert chat request to browser task request"""
        try:
            from app.api.browser_automation import BrowserTaskRequest

            # Map intent to task type and extract parameters
            intent_mapping = {
                "web_search": "navigation",
                "data_extraction": "extraction",
                "navigation": "navigation",
                "verification": "verification",
                "form_filling": "navigation",
                "monitoring": "custom_workflow",
                "comparison": "verification",
                "automation": "custom_workflow"
            }

            task_type = intent_mapping.get(request.detected_intent, "navigation")

            # Extract URL from parameters or context
            target_url = (request.extracted_parameters.get("url") or
                         request.context.get("current_url") or
                         request.extracted_parameters.get("website"))

            # Determine workflow type based on roles
            workflow_type = "hierarchical" if len(request.roles) > 1 else "sequential"

            # Create browser task request
            browser_task = BrowserTaskRequest(
                user_id=request.user_id,
                task_description=request.user_message,
                target_url=target_url,
                workflow_type=workflow_type,
                max_roles=min(len(request.roles), 10) if request.roles else 3,
                timeout_seconds=300,
                verify_results=request.detected_intent in ["verification", "comparison"],
                save_screenshots=True,  # Always save for chat integration
                custom_config={
                    "chat_integration": True,
                    "conversation_id": request.conversation_id,
                    "message_id": request.message_id,
                    "detected_intent": request.detected_intent,
                    "routing_strategy": request.routing_strategy,
                    "assigned_roles": request.roles,
                    "extracted_parameters": request.extracted_parameters
                }
            )

            return browser_task

        except Exception as e:
            self.log_error(f"Failed to convert chat to browser task: {e}")
            raise

    async def _execute_chat_browser_task(self, browser_task, background_tasks):
        """Execute browser task for chat integration"""
        try:
            # Route to appropriate browser automation endpoint based on task type
            if browser_task.custom_config.get("detected_intent") == "verification":
                return await browser_automation_api._execute_verification_task_sync(browser_task, background_tasks)
            elif browser_task.custom_config.get("detected_intent") == "data_extraction":
                return await browser_automation_api._execute_extraction_task_sync(browser_task, background_tasks)
            else:
                return await browser_automation_api._execute_navigation_task_sync(browser_task, background_tasks)

        except Exception as e:
            self.log_error(f"Failed to execute chat browser task: {e}")
            raise

    async def _generate_task_response_message(
        self,
        intent: str,
        task_response,
        quota_status: Dict[str, Any]
    ) -> str:
        """Generate response message for chat"""
        try:
            intent_messages = {
                "web_search": "🔍 I'm searching the web for that information...",
                "data_extraction": "📊 I'm extracting the data you requested...",
                "navigation": "🌐 I'm navigating to the website...",
                "verification": "✅ I'm verifying that information for you...",
                "form_filling": "📝 I'm filling out the form...",
                "monitoring": "👀 I'm setting up monitoring for you...",
                "comparison": "⚖️ I'm comparing those options...",
                "automation": "🤖 I'm setting up the automation..."
            }

            base_message = intent_messages.get(intent, "🚀 I'm working on your browser automation task...")

            # Add quota information
            remaining = quota_status.get("remaining_tasks", 0)
            quota_message = f" You have {remaining} browsing tasks remaining this month."

            # Add completion estimate
            completion_message = " I'll update you as I make progress!"

            return base_message + quota_message + completion_message

        except Exception as e:
            self.log_error(f"Failed to generate task response message: {e}")
            return "🚀 I'm working on your browser automation task. I'll update you with the results!"

    def _estimate_completion_time(self, intent: str) -> str:
        """Estimate task completion time"""
        try:
            time_estimates = {
                "web_search": "30-60 seconds",
                "data_extraction": "1-2 minutes",
                "navigation": "30-45 seconds",
                "verification": "1-3 minutes",
                "form_filling": "45-90 seconds",
                "monitoring": "2-5 minutes",
                "comparison": "2-4 minutes",
                "automation": "3-10 minutes"
            }

            return time_estimates.get(intent, "1-3 minutes")

        except Exception:
            return "1-3 minutes"

    async def _extract_intent_parameters(
        self,
        user_message: str,
        intent: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract parameters from user message based on intent"""
        try:
            parameters = {}

            # Basic URL extraction
            import re
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, user_message)
            if urls:
                parameters["url"] = urls[0]

            # Intent-specific parameter extraction
            if intent == "web_search":
                # Extract search query
                search_patterns = [
                    r'search for (.+)',
                    r'find (.+)',
                    r'look up (.+)',
                    r'research (.+)'
                ]
                for pattern in search_patterns:
                    match = re.search(pattern, user_message.lower())
                    if match:
                        parameters["search_query"] = match.group(1).strip()
                        break

            elif intent == "data_extraction":
                # Extract data type
                if "price" in user_message.lower():
                    parameters["data_type"] = "price"
                elif "contact" in user_message.lower():
                    parameters["data_type"] = "contact"
                elif "product" in user_message.lower():
                    parameters["data_type"] = "product"
                else:
                    parameters["data_type"] = "general"

            elif intent == "verification":
                # Extract verification criteria
                if "check if" in user_message.lower():
                    criteria_match = re.search(r'check if (.+)', user_message.lower())
                    if criteria_match:
                        parameters["verification_criteria"] = criteria_match.group(1).strip()

            # Add context parameters
            parameters.update(context)

            return parameters

        except Exception as e:
            self.log_error(f"Parameter extraction failed: {e}")
            return {}

    async def _recommend_action(self, intent: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Recommend specific action based on intent and parameters"""
        try:
            if not intent:
                return {"action": "clarify", "message": "Could you please clarify what you'd like me to help you with?"}

            recommendations = {
                "web_search": {
                    "action": "search",
                    "message": "I can search the web for that information using browser automation.",
                    "estimated_time": "30-60 seconds"
                },
                "data_extraction": {
                    "action": "extract",
                    "message": "I can extract that data from the website for you.",
                    "estimated_time": "1-2 minutes"
                },
                "navigation": {
                    "action": "navigate",
                    "message": "I can navigate to that website and gather information.",
                    "estimated_time": "30-45 seconds"
                },
                "verification": {
                    "action": "verify",
                    "message": "I can verify that information by checking multiple sources.",
                    "estimated_time": "1-3 minutes"
                }
            }

            return recommendations.get(intent, {
                "action": "general_browsing",
                "message": "I can help you with that using browser automation.",
                "estimated_time": "1-3 minutes"
            })

        except Exception as e:
            self.log_error(f"Action recommendation failed: {e}")
            return {
                "action": "error",
                "message": "I encountered an error while analyzing your request."
            }


# Create router instance
chat_integration_api = ChatIntegrationAPI()
router = chat_integration_api.router
