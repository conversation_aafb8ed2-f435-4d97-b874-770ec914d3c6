"""
Quota Enforcement Middleware
Middleware for enforcing tier-based access control and quota limits
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
import json

from app.core.logging import LoggerMixin
from app.services.access_control import access_control_manager, FeatureType, QuotaType


class QuotaEnforcementMiddleware(LoggerMixin):
    """
    Middleware for enforcing quota limits and access control
    
    Features:
    - Automatic quota checking for browser automation endpoints
    - Rate limiting based on subscription tier
    - Feature access validation
    - Usage tracking and analytics
    - Graceful quota exceeded responses
    """
    
    def __init__(self):
        # Rate limiting tracking
        self.rate_limit_cache: Dict[str, Dict[str, Any]] = {}
        
        # Endpoint to feature mapping
        self.endpoint_features = {
            "/api/browser/navigate": FeatureType.BASIC_BROWSING,
            "/api/browser/extract": FeatureType.ADVANCED_EXTRACTION,
            "/api/browser/verify": FeatureType.VERIFICATION,
            "/api/browser/workflow": FeatureType.CUSTOM_WORKFLOWS,
            "/api/browser/bulk": FeatureType.BULK_OPERATIONS
        }
        
        # Endpoint to quota mapping
        self.endpoint_quotas = {
            "/api/browser/navigate": QuotaType.BROWSING_TASKS,
            "/api/browser/extract": QuotaType.BROWSING_TASKS,
            "/api/browser/verify": QuotaType.VERIFICATION_REQUESTS,
            "/api/browser/workflow": QuotaType.CUSTOM_WORKFLOWS,
            "/api/browser/bulk": QuotaType.BROWSING_TASKS
        }
        
        self.log_info("Quota enforcement middleware initialized")
    
    async def __call__(self, request: Request, call_next):
        """Process request with quota enforcement"""
        try:
            # Skip quota enforcement for non-browser automation endpoints
            if not self._is_browser_automation_endpoint(request.url.path):
                return await call_next(request)
            
            # Extract user ID from request
            user_id = await self._extract_user_id(request)
            
            if not user_id:
                return JSONResponse(
                    status_code=401,
                    content={
                        "error": "Authentication required",
                        "message": "User ID not found in request"
                    }
                )
            
            # Check rate limits
            rate_limit_result = await self._check_rate_limits(user_id, request.url.path)
            
            if not rate_limit_result["allowed"]:
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "message": rate_limit_result["message"],
                        "retry_after": rate_limit_result["retry_after"]
                    }
                )
            
            # Check feature access
            feature = self.endpoint_features.get(request.url.path)
            
            if feature:
                access_result = await access_control_manager.check_access(
                    user_id=user_id,
                    feature=feature,
                    resource_requirements=await self._extract_resource_requirements(request)
                )
                
                if not access_result["access_granted"]:
                    return JSONResponse(
                        status_code=403,
                        content={
                            "error": "Access denied",
                            "message": access_result["reason"],
                            "tier": access_result["tier"],
                            "feature": access_result["feature"],
                            "required_tier": access_result.get("required_tier"),
                            "upgrade_url": "/upgrade"
                        }
                    )
            
            # Check quota before processing
            quota_type = self.endpoint_quotas.get(request.url.path)
            
            if quota_type:
                quota_check = await access_control_manager.check_browsing_quota(user_id)
                
                if not quota_check["can_browse"]:
                    return JSONResponse(
                        status_code=402,  # Payment Required
                        content={
                            "error": "Quota exceeded",
                            "message": quota_check["reason"],
                            "remaining_tasks": quota_check["remaining_tasks"],
                            "reset_date": quota_check.get("reset_date"),
                            "upgrade_url": "/upgrade"
                        }
                    )
            
            # Process the request
            start_time = time.time()
            
            try:
                response = await call_next(request)
                
                # If request was successful, consume quota
                if response.status_code < 400 and quota_type:
                    await self._consume_quota_after_success(
                        user_id, quota_type, request, response
                    )
                
                # Add quota headers to response
                await self._add_quota_headers(response, user_id)
                
                # Track usage
                await self._track_usage(user_id, request, response, time.time() - start_time)
                
                return response
                
            except Exception as e:
                self.log_error(f"Request processing failed: {e}")
                
                # Don't consume quota for failed requests
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": "Internal server error",
                        "message": "Request processing failed"
                    }
                )
                
        except Exception as e:
            self.log_error(f"Quota middleware error: {e}")
            
            # Allow request to proceed if middleware fails
            return await call_next(request)
    
    def _is_browser_automation_endpoint(self, path: str) -> bool:
        """Check if endpoint requires browser automation quota"""
        browser_automation_prefixes = [
            "/api/browser/",
            "/api/automation/",
            "/api/workflow/"
        ]
        
        return any(path.startswith(prefix) for prefix in browser_automation_prefixes)
    
    async def _extract_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request"""
        try:
            # Try to get from headers first
            user_id = request.headers.get("X-User-ID")
            
            if user_id:
                return user_id
            
            # Try to get from query parameters
            user_id = request.query_params.get("user_id")
            
            if user_id:
                return user_id
            
            # Try to get from request body
            if request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        data = json.loads(body)
                        user_id = data.get("user_id")
                        
                        if user_id:
                            return user_id
                except:
                    pass
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to extract user ID: {e}")
            return None
    
    async def _extract_resource_requirements(self, request: Request) -> Dict[str, Any]:
        """Extract resource requirements from request"""
        try:
            requirements = {}
            
            # Default requirements
            requirements["browsing_tasks"] = 1
            requirements["concurrent_sessions"] = 1
            
            # Try to get specific requirements from request body
            if request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        data = json.loads(body)
                        
                        # Check for bulk operations
                        if "tasks" in data and isinstance(data["tasks"], list):
                            requirements["browsing_tasks"] = len(data["tasks"])
                        
                        # Check for parallel processing
                        if data.get("parallel", False):
                            requirements["concurrent_sessions"] = data.get("max_parallel", 3)
                        
                        # Check for verification requests
                        if data.get("verify_results", False):
                            requirements["verification_requests"] = 1
                            
                except:
                    pass
            
            return requirements
            
        except Exception as e:
            self.log_error(f"Failed to extract resource requirements: {e}")
            return {"browsing_tasks": 1}
    
    async def _check_rate_limits(self, user_id: str, endpoint: str) -> Dict[str, Any]:
        """Check rate limits for user and endpoint"""
        try:
            # Get user's tier limits
            user_limits = await access_control_manager.get_user_limits(user_id)
            
            if "error" in user_limits:
                return {"allowed": True, "message": "Rate limit check skipped"}
            
            # Get rate limit for user's tier
            api_calls_per_hour = user_limits["limits"].get("api_calls_per_hour", 100)
            
            # Check current usage
            now = datetime.now()
            hour_key = now.strftime("%Y-%m-%d-%H")
            cache_key = f"{user_id}:{hour_key}"
            
            if cache_key not in self.rate_limit_cache:
                self.rate_limit_cache[cache_key] = {
                    "count": 0,
                    "first_request": now,
                    "last_request": now
                }
            
            rate_data = self.rate_limit_cache[cache_key]
            rate_data["count"] += 1
            rate_data["last_request"] = now
            
            if rate_data["count"] > api_calls_per_hour:
                # Calculate retry after (seconds until next hour)
                next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                retry_after = int((next_hour - now).total_seconds())
                
                return {
                    "allowed": False,
                    "message": f"API rate limit exceeded: {api_calls_per_hour} calls per hour",
                    "retry_after": retry_after
                }
            
            return {
                "allowed": True,
                "remaining": api_calls_per_hour - rate_data["count"]
            }
            
        except Exception as e:
            self.log_error(f"Rate limit check failed: {e}")
            return {"allowed": True, "message": "Rate limit check failed"}
    
    async def _consume_quota_after_success(
        self,
        user_id: str,
        quota_type: QuotaType,
        request: Request,
        response: Response
    ):
        """Consume quota after successful request"""
        try:
            # Determine amount to consume based on request
            amount = 1
            
            # For bulk operations, consume based on number of tasks
            if "bulk" in request.url.path:
                try:
                    body = await request.body()
                    if body:
                        data = json.loads(body)
                        if "tasks" in data and isinstance(data["tasks"], list):
                            amount = len(data["tasks"])
                except:
                    pass
            
            # Consume quota
            consumption_result = await access_control_manager.consume_quota(
                user_id=user_id,
                quota_type=quota_type,
                amount=amount,
                metadata={
                    "endpoint": request.url.path,
                    "method": request.method,
                    "timestamp": datetime.now().isoformat(),
                    "response_status": response.status_code
                }
            )
            
            if not consumption_result["success"]:
                self.log_warning(
                    f"Quota consumption failed for user {user_id}",
                    reason=consumption_result["reason"]
                )
            
        except Exception as e:
            self.log_error(f"Quota consumption failed: {e}")
    
    async def _add_quota_headers(self, response: Response, user_id: str):
        """Add quota information to response headers"""
        try:
            # Get current quota status
            quota_status = await access_control_manager.check_browsing_quota(user_id)
            
            if quota_status.get("can_browse") is not None:
                response.headers["X-Quota-Remaining"] = str(quota_status.get("remaining_tasks", 0))
                response.headers["X-Quota-Limit"] = str(quota_status.get("quota_limit", 0))
                response.headers["X-Quota-Reset"] = quota_status.get("reset_date", "")
                
                if quota_status.get("percentage_used") is not None:
                    response.headers["X-Quota-Used-Percent"] = f"{quota_status['percentage_used']:.1f}"
            
        except Exception as e:
            self.log_warning(f"Failed to add quota headers: {e}")
    
    async def _track_usage(
        self,
        user_id: str,
        request: Request,
        response: Response,
        duration: float
    ):
        """Track usage for analytics"""
        try:
            usage_data = {
                "user_id": user_id,
                "endpoint": request.url.path,
                "method": request.method,
                "status_code": response.status_code,
                "duration": duration,
                "timestamp": datetime.now().isoformat(),
                "user_agent": request.headers.get("User-Agent", ""),
                "ip_address": request.client.host if request.client else ""
            }
            
            # This would typically be sent to an analytics service
            self.log_info(f"Usage tracked for user {user_id}", endpoint=request.url.path)
            
        except Exception as e:
            self.log_warning(f"Usage tracking failed: {e}")
    
    async def cleanup_rate_limit_cache(self):
        """Clean up old rate limit cache entries"""
        try:
            now = datetime.now()
            current_hour = now.strftime("%Y-%m-%d-%H")
            
            # Remove entries older than current hour
            expired_keys = [
                key for key in self.rate_limit_cache.keys()
                if not key.endswith(current_hour)
            ]
            
            for key in expired_keys:
                del self.rate_limit_cache[key]
                
            if expired_keys:
                self.log_info(f"Cleaned up {len(expired_keys)} expired rate limit entries")
                
        except Exception as e:
            self.log_error(f"Rate limit cache cleanup failed: {e}")


# Global quota enforcement middleware instance
quota_middleware = QuotaEnforcementMiddleware()
