#!/usr/bin/env python3
"""
Test RouKey Integration for Milestone 2
Tests the integration between the main RouKey app (port 3000) and browser automation service (port 8001)
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

# Real Pro User Data
USER_ID = "69d967d5-0b7b-402b-ae1b-711d9b74eef4"
CONFIG_ID = "b39270c0-feb8-4c99-b6d2-9ee224edd57e"
USER_EMAIL = "<EMAIL>"

async def test_rokey_integration():
    """Test the integration between RouKey app and browser automation service"""
    print("🔗 TESTING ROKEY INTEGRATION")
    print("=" * 50)
    print(f"RouKey App: http://localhost:3000")
    print(f"Browser Service: http://localhost:8001")
    print(f"User: {USER_EMAIL}")
    print("=" * 50)
    
    rokey_url = "http://localhost:3000"
    browser_url = "http://localhost:8001"
    
    async with httpx.AsyncClient(timeout=300.0) as client:
        
        # 1. Test RouKey app health
        print("\n1️⃣ Testing RouKey App Health...")
        try:
            response = await client.get(f"{rokey_url}/api/health")
            if response.status_code == 200:
                print("✅ RouKey app is running")
            else:
                print(f"❌ RouKey app health failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Cannot connect to RouKey app: {e}")
            print("⚠️ Make sure RouKey app is running on port 3000")
            
        # 2. Test browser automation service health
        print("\n2️⃣ Testing Browser Automation Service Health...")
        try:
            response = await client.get(f"{browser_url}/health/ready")
            if response.status_code == 200:
                print("✅ Browser automation service is running")
            else:
                print(f"❌ Browser service health failed: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Cannot connect to browser service: {e}")
            return
            
        # 3. Test user configuration retrieval
        print("\n3️⃣ Testing User Configuration Retrieval...")
        try:
            # Test if we can get user config from RouKey
            response = await client.get(f"{rokey_url}/api/user/{USER_ID}/config/{CONFIG_ID}")
            if response.status_code == 200:
                config = response.json()
                print("✅ User configuration retrieved from RouKey")
                print(f"   Config name: {config.get('name', 'Unknown')}")
                print(f"   Routing strategy: {config.get('routing_strategy', 'Unknown')}")
                print(f"   Browser automation: {config.get('browser_automation_enabled', False)}")
            else:
                print(f"⚠️ User config retrieval failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ User config error: {e}")
            
        # 4. Test API keys retrieval
        print("\n4️⃣ Testing API Keys Retrieval...")
        try:
            response = await client.get(f"{rokey_url}/api/user/{USER_ID}/api-keys")
            if response.status_code == 200:
                keys = response.json()
                print(f"✅ Retrieved {len(keys)} API keys from RouKey")
                for key in keys[:3]:  # Show first 3
                    print(f"   - {key.get('provider', 'Unknown')}: {key.get('label', 'Unlabeled')}")
            else:
                print(f"⚠️ API keys retrieval failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ API keys error: {e}")
            
        # 5. Test role classification
        print("\n5️⃣ Testing Role Classification...")
        try:
            classification_request = {
                "task": "when does messi play his next match and did he score in his last?",
                "user_id": USER_ID,
                "config_id": CONFIG_ID
            }
            
            response = await client.post(
                f"{rokey_url}/api/classify-roles",
                json=classification_request
            )
            
            if response.status_code == 200:
                roles = response.json()
                print(f"✅ Role classification successful")
                print(f"   Classified roles: {', '.join(roles.get('roles', []))}")
                print(f"   Complexity: {roles.get('complexity', 'Unknown')}")
            else:
                print(f"⚠️ Role classification failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Role classification error: {e}")
            
        # 6. Test end-to-end browser automation via RouKey
        print("\n6️⃣ Testing End-to-End Browser Automation via RouKey...")
        try:
            # This would be the actual request that RouKey makes to the browser service
            browser_request = {
                "user_id": USER_ID,
                "config_id": CONFIG_ID,
                "task": "what's the weather like in Paris today?",
                "enable_browser_automation": True
            }
            
            print("📝 Task: what's the weather like in Paris today?")
            print("🔄 Sending request through RouKey...")
            
            start_time = time.time()
            
            response = await client.post(
                f"{rokey_url}/api/chat/browser-automation",
                json=browser_request
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ End-to-end test successful in {execution_time:.2f}s")
                
                # Show result details
                if "browser_result" in result:
                    browser_result = result["browser_result"]
                    print(f"   Browser task success: {browser_result.get('success', False)}")
                    print(f"   Steps completed: {browser_result.get('steps_completed', 0)}")
                    
                final_response = result.get("response", "")
                if final_response:
                    print(f"📋 Response: {final_response[:200]}...")
                    
            else:
                print(f"❌ End-to-end test failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ End-to-end test error: {e}")
            
        # 7. Test direct browser service call
        print("\n7️⃣ Testing Direct Browser Service Call...")
        try:
            direct_request = {
                "user_id": USER_ID,
                "config_id": CONFIG_ID,
                "user_tier": "professional",
                "task": "find the current time in Tokyo",
                "workflow_type": "sequential",
                "routing_strategy": "intelligent_role",
                "max_roles": 2,
                "max_steps": 5,
                "timeout_seconds": 120,
                "enable_memory": True,
                "headless": True,
                "verify_results": True
            }
            
            print("📝 Task: find the current time in Tokyo")
            print("🔄 Direct call to browser service...")
            
            start_time = time.time()
            
            response = await client.post(
                f"{browser_url}/api/v1/browser/execute",
                json=direct_request
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Direct browser call successful in {execution_time:.2f}s")
                print(f"   Success: {result.get('success', False)}")
                
                final_result = result.get("result", "")
                if final_result:
                    print(f"📋 Result: {final_result[:150]}...")
                    
            else:
                print(f"❌ Direct browser call failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Direct browser call error: {e}")
            
        # 8. Test quota and usage tracking
        print("\n8️⃣ Testing Quota and Usage Tracking...")
        try:
            response = await client.get(f"{rokey_url}/api/user/{USER_ID}/browser-automation-usage")
            if response.status_code == 200:
                usage = response.json()
                print("✅ Usage tracking retrieved")
                print(f"   Monthly usage: {usage.get('monthly_usage', 0)}")
                print(f"   Monthly limit: {usage.get('monthly_limit', 0)}")
                print(f"   Remaining: {usage.get('remaining', 0)}")
            else:
                print(f"⚠️ Usage tracking failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Usage tracking error: {e}")
            
    print("\n" + "=" * 50)
    print("🏁 ROKEY INTEGRATION TEST COMPLETED")
    print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    print("\n📋 INTEGRATION CHECKLIST:")
    print("□ RouKey app running on port 3000")
    print("□ Browser service running on port 8001")
    print("□ User configuration retrieval working")
    print("□ API keys retrieval working")
    print("□ Role classification working")
    print("□ End-to-end browser automation working")
    print("□ Direct browser service calls working")
    print("□ Usage tracking working")

if __name__ == "__main__":
    asyncio.run(test_rokey_integration())
