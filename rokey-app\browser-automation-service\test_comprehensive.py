#!/usr/bin/env python3
"""
Comprehensive end-to-end testing of browser automation with real Pro user
"""

import asyncio
import httpx
import json
from datetime import datetime

# Real Pro user data from Supabase
TEST_USER_ID = "69d967d5-0b7b-402b-ae1b-711d9b74eef4"
TEST_CONFIG_ID = "b39270c0-feb8-4c99-b6d2-9ee224edd57e"
TEST_CONFIG_NAME = "openrouter"

# Test tasks for comprehensive validation
TEST_TASKS = [
    {
        "name": "Messi Match Query",
        "task": "when does messi play his next match and did he score in his last?",
        "expected_actions": ["search", "navigate", "extract_data", "verify"],
        "timeout": 300
    },
    {
        "name": "Shopping Research",
        "task": "find the cheapest wireless headphones on Amazon under $50",
        "expected_actions": ["navigate", "search", "filter", "compare"],
        "timeout": 300
    },
    {
        "name": "Weather Information",
        "task": "what's the weather like in New York today?",
        "expected_actions": ["search", "extract_data"],
        "timeout": 180
    },
    {
        "name": "Stock Price Check",
        "task": "what is the current price of Tesla stock?",
        "expected_actions": ["search", "navigate", "extract_data"],
        "timeout": 180
    },
    {
        "name": "News Research",
        "task": "find the latest news about artificial intelligence breakthroughs",
        "expected_actions": ["search", "navigate", "extract_data", "summarize"],
        "timeout": 240
    }
]

async def test_browser_automation_task(client: httpx.AsyncClient, task_data: dict):
    """Test a single browser automation task"""
    
    print(f"\n🧪 Testing: {task_data['name']}")
    print(f"📝 Task: {task_data['task']}")
    
    # Prepare request payload
    payload = {
        "task": task_data["task"],
        "task_type": "research",
        "user_id": TEST_USER_ID,
        "config_id": TEST_CONFIG_ID,
        "config_name": TEST_CONFIG_NAME,
        "user_tier": "pro",
        "extracted_parameters": {
            "headless": True,
            "viewport_width": 1920,
            "viewport_height": 1080
        },
        "stream": False
    }
    
    start_time = datetime.now()
    
    try:
        # Make request to browser automation service
        print("🚀 Sending request to browser automation service...")
        
        response = await client.post(
            "http://localhost:8001/api/v1/browser/execute",
            json=payload,
            timeout=task_data["timeout"]
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏱️  Response time: {duration:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Task completed successfully!")
            print(f"📋 Result: {result.get('final_result', 'No result')[:200]}...")
            
            # Check if expected actions were performed
            metadata = result.get('execution_metadata', {})
            steps_completed = metadata.get('steps_completed', 0)
            print(f"🔢 Steps completed: {steps_completed}")
            
            if steps_completed > 0:
                print("✅ Browser automation executed steps")
            else:
                print("⚠️  No steps were executed")
            
            return True
            
        else:
            print(f"❌ Task failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except httpx.TimeoutException:
        print(f"⏰ Task timed out after {task_data['timeout']} seconds")
        return False
    except Exception as e:
        print(f"❌ Task failed with error: {e}")
        return False

async def test_service_health():
    """Test if the browser automation service is healthy"""
    
    print("🏥 Checking service health...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8001/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print("✅ Service is healthy!")
                print(f"📊 Health status: {health_data}")
                return True
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

async def run_comprehensive_tests():
    """Run comprehensive end-to-end tests"""
    
    print("🎯 Starting Comprehensive Browser Automation Testing")
    print("=" * 60)
    print(f"👤 Test User ID: {TEST_USER_ID}")
    print(f"⚙️  Test Config: {TEST_CONFIG_NAME} ({TEST_CONFIG_ID})")
    print(f"📅 Test Time: {datetime.now().isoformat()}")
    print("=" * 60)
    
    # Check service health first
    if not await test_service_health():
        print("❌ Service health check failed. Aborting tests.")
        return
    
    # Run all test tasks
    results = []
    
    async with httpx.AsyncClient() as client:
        for i, task_data in enumerate(TEST_TASKS, 1):
            print(f"\n📋 Test {i}/{len(TEST_TASKS)}")
            success = await test_browser_automation_task(client, task_data)
            results.append({
                "task": task_data["name"],
                "success": success
            })
            
            # Wait between tests
            if i < len(TEST_TASKS):
                print("⏳ Waiting 5 seconds before next test...")
                await asyncio.sleep(5)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"✅ Successful tests: {successful_tests}/{total_tests}")
    print(f"📈 Success rate: {success_rate:.1f}%")
    
    print("\n📋 Individual Results:")
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"  {status} - {result['task']}")
    
    if success_rate >= 80:
        print("\n🎉 COMPREHENSIVE TESTING PASSED!")
        print("🚀 Browser automation is ready for production!")
    else:
        print("\n⚠️  COMPREHENSIVE TESTING NEEDS IMPROVEMENT")
        print("🔧 Some issues need to be addressed before production")

if __name__ == "__main__":
    asyncio.run(run_comprehensive_tests())
