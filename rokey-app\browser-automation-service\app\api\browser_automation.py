"""
Browser Automation API Endpoints
Main API endpoints for browser automation functionality with LangGraph integration
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
import uuid

from app.core.logging import LoggerMixin
from app.services.browser_orchestrator import BrowserOrchestrator
from app.services.access_control import access_control_manager, FeatureType
from app.services.error_handler import error_handler
from app.services.fallback_manager import fallback_manager


class BrowserTaskRequest(BaseModel):
    """Request model for browser automation tasks"""
    user_id: str = Field(..., description="User identifier")
    task_description: str = Field(..., description="Natural language description of the task")
    target_url: Optional[str] = Field(None, description="Starting URL for the task")
    workflow_type: str = Field("sequential", description="Workflow type: sequential, hierarchical, or supervisor")
    max_roles: int = Field(3, description="Maximum number of roles to use")
    timeout_seconds: int = Field(300, description="Task timeout in seconds")
    verify_results: bool = Field(True, description="Whether to verify results")
    save_screenshots: bool = Field(False, description="Whether to save screenshots")
    custom_config: Optional[Dict[str, Any]] = Field(None, description="Custom configuration options")


class BulkBrowserTaskRequest(BaseModel):
    """Request model for bulk browser automation tasks"""
    user_id: str = Field(..., description="User identifier")
    tasks: List[Dict[str, Any]] = Field(..., description="List of tasks to execute")
    parallel_execution: bool = Field(False, description="Whether to execute tasks in parallel")
    max_parallel: int = Field(3, description="Maximum parallel tasks")
    batch_size: int = Field(10, description="Batch size for processing")
    continue_on_error: bool = Field(True, description="Whether to continue if individual tasks fail")


class WorkflowRequest(BaseModel):
    """Request model for custom workflow execution"""
    user_id: str = Field(..., description="User identifier")
    workflow_name: str = Field(..., description="Name of the workflow")
    workflow_steps: List[Dict[str, Any]] = Field(..., description="Workflow steps definition")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="Input data for workflow")
    role_assignments: Optional[Dict[str, str]] = Field(None, description="Role assignments for steps")


class TaskResponse(BaseModel):
    """Response model for browser automation tasks"""
    task_id: str
    user_id: str
    status: str
    created_at: str
    updated_at: str
    progress: float
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    quota_consumed: int = 1


class BrowserAutomationAPI(LoggerMixin):
    """API endpoints for browser automation functionality"""
    
    def __init__(self):
        self.router = APIRouter(prefix="/api/browser", tags=["browser-automation"])
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self._setup_routes()
        self.log_info("Browser automation API initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.router.post("/navigate", response_model=TaskResponse)
        async def navigate_and_extract(
            request: BrowserTaskRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Navigate to a URL and extract information
            
            Args:
                request: Browser task request
                background_tasks: FastAPI background tasks
                
            Returns:
                Task response with execution details
            """
            try:
                self.log_info(f"Navigation task requested by user: {request.user_id}")
                
                # Check access and quota
                access_check = await self._check_access_and_quota(
                    request.user_id, 
                    FeatureType.BASIC_BROWSING,
                    {"browsing_tasks": 1}
                )
                
                if not access_check["allowed"]:
                    raise HTTPException(
                        status_code=access_check["status_code"],
                        detail=access_check["error"]
                    )
                
                # Create task
                task_id = str(uuid.uuid4())
                task_data = {
                    "task_id": task_id,
                    "user_id": request.user_id,
                    "task_type": "navigation",
                    "status": "pending",
                    "created_at": datetime.now(),
                    "request": request.dict(),
                    "progress": 0.0
                }
                
                self.active_tasks[task_id] = task_data
                
                # Execute task in background
                background_tasks.add_task(
                    self._execute_navigation_task,
                    task_id,
                    request
                )
                
                return TaskResponse(
                    task_id=task_id,
                    user_id=request.user_id,
                    status="pending",
                    created_at=task_data["created_at"].isoformat(),
                    updated_at=task_data["created_at"].isoformat(),
                    progress=0.0
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Navigation task creation failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create navigation task: {e}"
                )
        
        @self.router.post("/extract", response_model=TaskResponse)
        async def extract_data(
            request: BrowserTaskRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Extract specific data from a webpage
            
            Args:
                request: Browser task request
                background_tasks: FastAPI background tasks
                
            Returns:
                Task response with extraction results
            """
            try:
                self.log_info(f"Data extraction task requested by user: {request.user_id}")
                
                # Check access and quota
                access_check = await self._check_access_and_quota(
                    request.user_id,
                    FeatureType.ADVANCED_EXTRACTION,
                    {"browsing_tasks": 1}
                )
                
                if not access_check["allowed"]:
                    raise HTTPException(
                        status_code=access_check["status_code"],
                        detail=access_check["error"]
                    )
                
                # Create task
                task_id = str(uuid.uuid4())
                task_data = {
                    "task_id": task_id,
                    "user_id": request.user_id,
                    "task_type": "extraction",
                    "status": "pending",
                    "created_at": datetime.now(),
                    "request": request.dict(),
                    "progress": 0.0
                }
                
                self.active_tasks[task_id] = task_data
                
                # Execute task in background
                background_tasks.add_task(
                    self._execute_extraction_task,
                    task_id,
                    request
                )
                
                return TaskResponse(
                    task_id=task_id,
                    user_id=request.user_id,
                    status="pending",
                    created_at=task_data["created_at"].isoformat(),
                    updated_at=task_data["created_at"].isoformat(),
                    progress=0.0
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Extraction task creation failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create extraction task: {e}"
                )
        
        @self.router.post("/verify", response_model=TaskResponse)
        async def verify_information(
            request: BrowserTaskRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Verify information using web search and browsing
            
            Args:
                request: Browser task request
                background_tasks: FastAPI background tasks
                
            Returns:
                Task response with verification results
            """
            try:
                self.log_info(f"Verification task requested by user: {request.user_id}")
                
                # Check access and quota
                access_check = await self._check_access_and_quota(
                    request.user_id,
                    FeatureType.VERIFICATION,
                    {"verification_requests": 1, "browsing_tasks": 1}
                )
                
                if not access_check["allowed"]:
                    raise HTTPException(
                        status_code=access_check["status_code"],
                        detail=access_check["error"]
                    )
                
                # Create task
                task_id = str(uuid.uuid4())
                task_data = {
                    "task_id": task_id,
                    "user_id": request.user_id,
                    "task_type": "verification",
                    "status": "pending",
                    "created_at": datetime.now(),
                    "request": request.dict(),
                    "progress": 0.0
                }
                
                self.active_tasks[task_id] = task_data
                
                # Execute task in background
                background_tasks.add_task(
                    self._execute_verification_task,
                    task_id,
                    request
                )
                
                return TaskResponse(
                    task_id=task_id,
                    user_id=request.user_id,
                    status="pending",
                    created_at=task_data["created_at"].isoformat(),
                    updated_at=task_data["created_at"].isoformat(),
                    progress=0.0
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Verification task creation failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create verification task: {e}"
                )
        
        @self.router.post("/workflow", response_model=TaskResponse)
        async def execute_custom_workflow(
            request: WorkflowRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Execute a custom browser automation workflow
            
            Args:
                request: Workflow request
                background_tasks: FastAPI background tasks
                
            Returns:
                Task response with workflow execution details
            """
            try:
                self.log_info(f"Custom workflow requested by user: {request.user_id}")
                
                # Check access and quota
                access_check = await self._check_access_and_quota(
                    request.user_id,
                    FeatureType.CUSTOM_WORKFLOWS,
                    {"custom_workflows": 1, "browsing_tasks": len(request.workflow_steps)}
                )
                
                if not access_check["allowed"]:
                    raise HTTPException(
                        status_code=access_check["status_code"],
                        detail=access_check["error"]
                    )
                
                # Create task
                task_id = str(uuid.uuid4())
                task_data = {
                    "task_id": task_id,
                    "user_id": request.user_id,
                    "task_type": "custom_workflow",
                    "status": "pending",
                    "created_at": datetime.now(),
                    "request": request.dict(),
                    "progress": 0.0
                }
                
                self.active_tasks[task_id] = task_data
                
                # Execute workflow in background
                background_tasks.add_task(
                    self._execute_custom_workflow,
                    task_id,
                    request
                )
                
                return TaskResponse(
                    task_id=task_id,
                    user_id=request.user_id,
                    status="pending",
                    created_at=task_data["created_at"].isoformat(),
                    updated_at=task_data["created_at"].isoformat(),
                    progress=0.0
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Custom workflow creation failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create custom workflow: {e}"
                )
        
        @self.router.post("/bulk", response_model=Dict[str, Any])
        async def execute_bulk_tasks(
            request: BulkBrowserTaskRequest,
            background_tasks: BackgroundTasks
        ):
            """
            Execute multiple browser automation tasks
            
            Args:
                request: Bulk task request
                background_tasks: FastAPI background tasks
                
            Returns:
                Bulk execution response with task IDs
            """
            try:
                self.log_info(f"Bulk tasks requested by user: {request.user_id}, count: {len(request.tasks)}")
                
                # Check access and quota
                access_check = await self._check_access_and_quota(
                    request.user_id,
                    FeatureType.BULK_OPERATIONS,
                    {"browsing_tasks": len(request.tasks)}
                )
                
                if not access_check["allowed"]:
                    raise HTTPException(
                        status_code=access_check["status_code"],
                        detail=access_check["error"]
                    )
                
                # Create bulk task
                bulk_task_id = str(uuid.uuid4())
                task_ids = []
                
                # Create individual tasks
                for i, task_data in enumerate(request.tasks):
                    task_id = str(uuid.uuid4())
                    task_ids.append(task_id)
                    
                    individual_task = {
                        "task_id": task_id,
                        "user_id": request.user_id,
                        "task_type": "bulk_item",
                        "bulk_task_id": bulk_task_id,
                        "status": "pending",
                        "created_at": datetime.now(),
                        "task_data": task_data,
                        "progress": 0.0
                    }
                    
                    self.active_tasks[task_id] = individual_task
                
                # Create bulk task tracker
                bulk_task = {
                    "bulk_task_id": bulk_task_id,
                    "user_id": request.user_id,
                    "task_type": "bulk",
                    "status": "pending",
                    "created_at": datetime.now(),
                    "total_tasks": len(request.tasks),
                    "completed_tasks": 0,
                    "failed_tasks": 0,
                    "task_ids": task_ids,
                    "request": request.dict(),
                    "progress": 0.0
                }
                
                self.active_tasks[bulk_task_id] = bulk_task
                
                # Execute bulk tasks in background
                background_tasks.add_task(
                    self._execute_bulk_tasks,
                    bulk_task_id,
                    request
                )
                
                return {
                    "bulk_task_id": bulk_task_id,
                    "user_id": request.user_id,
                    "status": "pending",
                    "total_tasks": len(request.tasks),
                    "task_ids": task_ids,
                    "created_at": bulk_task["created_at"].isoformat(),
                    "parallel_execution": request.parallel_execution,
                    "max_parallel": request.max_parallel
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Bulk task creation failed: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create bulk tasks: {e}"
                )
        
        @self.router.get("/task/{task_id}", response_model=TaskResponse)
        async def get_task_status(task_id: str):
            """
            Get the status of a specific task
            
            Args:
                task_id: Task identifier
                
            Returns:
                Current task status and results
            """
            try:
                if task_id not in self.active_tasks:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Task not found: {task_id}"
                    )
                
                task_data = self.active_tasks[task_id]
                
                return TaskResponse(
                    task_id=task_id,
                    user_id=task_data["user_id"],
                    status=task_data["status"],
                    created_at=task_data["created_at"].isoformat(),
                    updated_at=task_data.get("updated_at", task_data["created_at"]).isoformat(),
                    progress=task_data["progress"],
                    result=task_data.get("result"),
                    error=task_data.get("error"),
                    execution_time=task_data.get("execution_time"),
                    quota_consumed=task_data.get("quota_consumed", 1)
                )
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to get task status: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve task status: {e}"
                )
        
        @self.router.get("/tasks/{user_id}")
        async def get_user_tasks(
            user_id: str,
            status: Optional[str] = None,
            limit: int = 50,
            offset: int = 0
        ):
            """
            Get tasks for a specific user
            
            Args:
                user_id: User identifier
                status: Optional status filter
                limit: Maximum number of tasks to return
                offset: Offset for pagination
                
            Returns:
                List of user tasks
            """
            try:
                user_tasks = []
                
                for task_id, task_data in self.active_tasks.items():
                    if task_data["user_id"] == user_id:
                        if status is None or task_data["status"] == status:
                            user_tasks.append({
                                "task_id": task_id,
                                "task_type": task_data["task_type"],
                                "status": task_data["status"],
                                "created_at": task_data["created_at"].isoformat(),
                                "progress": task_data["progress"],
                                "has_result": "result" in task_data,
                                "has_error": "error" in task_data
                            })
                
                # Sort by creation time (newest first)
                user_tasks.sort(key=lambda x: x["created_at"], reverse=True)
                
                # Apply pagination
                paginated_tasks = user_tasks[offset:offset + limit]
                
                return {
                    "user_id": user_id,
                    "tasks": paginated_tasks,
                    "total_count": len(user_tasks),
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < len(user_tasks)
                }
                
            except Exception as e:
                self.log_error(f"Failed to get user tasks: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve user tasks: {e}"
                )
        
        @self.router.delete("/task/{task_id}")
        async def cancel_task(task_id: str):
            """
            Cancel a running task
            
            Args:
                task_id: Task identifier
                
            Returns:
                Cancellation confirmation
            """
            try:
                if task_id not in self.active_tasks:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Task not found: {task_id}"
                    )
                
                task_data = self.active_tasks[task_id]
                
                if task_data["status"] in ["completed", "failed", "cancelled"]:
                    return {
                        "task_id": task_id,
                        "status": task_data["status"],
                        "message": f"Task already {task_data['status']}"
                    }
                
                # Mark task as cancelled
                task_data["status"] = "cancelled"
                task_data["updated_at"] = datetime.now()
                task_data["error"] = "Task cancelled by user"
                
                self.log_info(f"Task cancelled: {task_id}")
                
                return {
                    "task_id": task_id,
                    "status": "cancelled",
                    "message": "Task cancelled successfully"
                }
                
            except HTTPException:
                raise
            except Exception as e:
                self.log_error(f"Failed to cancel task: {e}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to cancel task: {e}"
                )

    # Helper methods for access control and task execution
    async def _check_access_and_quota(
        self,
        user_id: str,
        feature: FeatureType,
        resource_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check user access and quota for a feature"""
        try:
            # Check feature access
            access_result = await access_control_manager.check_access(
                user_id=user_id,
                feature=feature,
                resource_requirements=resource_requirements
            )

            if not access_result["access_granted"]:
                return {
                    "allowed": False,
                    "status_code": 403,
                    "error": {
                        "error": "Access denied",
                        "message": access_result["reason"],
                        "tier": access_result["tier"],
                        "feature": access_result["feature"],
                        "required_tier": access_result.get("required_tier"),
                        "upgrade_url": "/upgrade"
                    }
                }

            # Check browsing quota specifically
            if "browsing_tasks" in resource_requirements:
                quota_check = await access_control_manager.check_browsing_quota(user_id)

                if not quota_check["can_browse"]:
                    return {
                        "allowed": False,
                        "status_code": 402,  # Payment Required
                        "error": {
                            "error": "Quota exceeded",
                            "message": quota_check["reason"],
                            "remaining_tasks": quota_check["remaining_tasks"],
                            "reset_date": quota_check.get("reset_date"),
                            "upgrade_url": "/upgrade"
                        }
                    }

            return {"allowed": True}

        except Exception as e:
            self.log_error(f"Access check failed: {e}")
            return {
                "allowed": False,
                "status_code": 500,
                "error": {"error": "Access check failed", "message": str(e)}
            }

    async def _execute_navigation_task(self, task_id: str, request: BrowserTaskRequest):
        """Execute navigation task using LangGraph workflow"""
        try:
            task_data = self.active_tasks[task_id]
            task_data["status"] = "running"
            task_data["updated_at"] = datetime.now()

            self.log_info(f"Starting navigation task: {task_id}")

            # Prepare workflow input
            workflow_input = {
                "task_id": task_id,
                "user_id": request.user_id,
                "task_type": "navigation",
                "task_description": request.task_description,
                "target_url": request.target_url,
                "workflow_type": request.workflow_type,
                "max_roles": request.max_roles,
                "timeout_seconds": request.timeout_seconds,
                "verify_results": request.verify_results,
                "save_screenshots": request.save_screenshots,
                "custom_config": request.custom_config or {}
            }

            # Execute workflow with error handling
            start_time = datetime.now()

            try:
                # Execute browser orchestration workflow
                orchestrator = BrowserOrchestrator()
                result = await orchestrator.execute_task(
                    task_description=request.task_description,
                    user_config={"user_id": request.user_id},
                    workflow_type=request.workflow_type,
                    max_roles=request.max_roles
                )

                execution_time = (datetime.now() - start_time).total_seconds()

                # Update task with success
                task_data["status"] = "completed"
                task_data["result"] = result
                task_data["execution_time"] = execution_time
                task_data["progress"] = 100.0
                task_data["updated_at"] = datetime.now()

                # Consume quota
                await self._consume_task_quota(request.user_id, "browsing_tasks", 1, task_id)

                self.log_info(f"Navigation task completed: {task_id}", execution_time=execution_time)

            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()

                # Handle error with fallback
                error_result = await self._handle_task_error(task_id, e, request)

                task_data["status"] = "failed"
                task_data["error"] = str(e)
                task_data["execution_time"] = execution_time
                task_data["error_details"] = error_result
                task_data["updated_at"] = datetime.now()

                self.log_error(f"Navigation task failed: {task_id}", error=str(e))

        except Exception as e:
            self.log_error(f"Navigation task execution failed: {e}")
            task_data["status"] = "failed"
            task_data["error"] = f"Task execution failed: {e}"
            task_data["updated_at"] = datetime.now()

    async def _execute_extraction_task(self, task_id: str, request: BrowserTaskRequest):
        """Execute data extraction task using LangGraph workflow"""
        try:
            task_data = self.active_tasks[task_id]
            task_data["status"] = "running"
            task_data["updated_at"] = datetime.now()

            self.log_info(f"Starting extraction task: {task_id}")

            # Prepare workflow input
            workflow_input = {
                "task_id": task_id,
                "user_id": request.user_id,
                "task_type": "extraction",
                "task_description": request.task_description,
                "target_url": request.target_url,
                "workflow_type": request.workflow_type,
                "max_roles": request.max_roles,
                "timeout_seconds": request.timeout_seconds,
                "verify_results": request.verify_results,
                "save_screenshots": request.save_screenshots,
                "custom_config": request.custom_config or {}
            }

            start_time = datetime.now()

            try:
                # Execute browser orchestration workflow
                orchestrator = BrowserOrchestrator()
                result = await orchestrator.execute_task(
                    task_description=request.task_description,
                    user_config={"user_id": request.user_id},
                    workflow_type=request.workflow_type,
                    max_roles=request.max_roles
                )

                execution_time = (datetime.now() - start_time).total_seconds()

                # Update task with success
                task_data["status"] = "completed"
                task_data["result"] = result
                task_data["execution_time"] = execution_time
                task_data["progress"] = 100.0
                task_data["updated_at"] = datetime.now()

                # Consume quota
                await self._consume_task_quota(request.user_id, "browsing_tasks", 1, task_id)

                self.log_info(f"Extraction task completed: {task_id}", execution_time=execution_time)

            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()

                # Handle error with fallback
                error_result = await self._handle_task_error(task_id, e, request)

                task_data["status"] = "failed"
                task_data["error"] = str(e)
                task_data["execution_time"] = execution_time
                task_data["error_details"] = error_result
                task_data["updated_at"] = datetime.now()

                self.log_error(f"Extraction task failed: {task_id}", error=str(e))

        except Exception as e:
            self.log_error(f"Extraction task execution failed: {e}")
            task_data["status"] = "failed"
            task_data["error"] = f"Task execution failed: {e}"
            task_data["updated_at"] = datetime.now()

    async def _execute_verification_task(self, task_id: str, request: BrowserTaskRequest):
        """Execute verification task using LangGraph workflow"""
        try:
            task_data = self.active_tasks[task_id]
            task_data["status"] = "running"
            task_data["updated_at"] = datetime.now()

            self.log_info(f"Starting verification task: {task_id}")

            # Prepare workflow input
            workflow_input = {
                "task_id": task_id,
                "user_id": request.user_id,
                "task_type": "verification",
                "task_description": request.task_description,
                "target_url": request.target_url,
                "workflow_type": request.workflow_type,
                "max_roles": request.max_roles,
                "timeout_seconds": request.timeout_seconds,
                "verify_results": True,  # Always verify for verification tasks
                "save_screenshots": request.save_screenshots,
                "custom_config": request.custom_config or {}
            }

            start_time = datetime.now()

            try:
                # Execute browser orchestration workflow
                orchestrator = BrowserOrchestrator()
                result = await orchestrator.execute_task(
                    task_description=request.task_description,
                    user_config={"user_id": request.user_id},
                    workflow_type=request.workflow_type,
                    max_roles=request.max_roles
                )

                execution_time = (datetime.now() - start_time).total_seconds()

                # Update task with success
                task_data["status"] = "completed"
                task_data["result"] = result
                task_data["execution_time"] = execution_time
                task_data["progress"] = 100.0
                task_data["updated_at"] = datetime.now()

                # Consume quota (both verification and browsing)
                await self._consume_task_quota(request.user_id, "verification_requests", 1, task_id)
                await self._consume_task_quota(request.user_id, "browsing_tasks", 1, task_id)

                self.log_info(f"Verification task completed: {task_id}", execution_time=execution_time)

            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()

                # Handle error with fallback
                error_result = await self._handle_task_error(task_id, e, request)

                task_data["status"] = "failed"
                task_data["error"] = str(e)
                task_data["execution_time"] = execution_time
                task_data["error_details"] = error_result
                task_data["updated_at"] = datetime.now()

                self.log_error(f"Verification task failed: {task_id}", error=str(e))

        except Exception as e:
            self.log_error(f"Verification task execution failed: {e}")
            task_data["status"] = "failed"
            task_data["error"] = f"Task execution failed: {e}"
            task_data["updated_at"] = datetime.now()

    async def _execute_custom_workflow(self, task_id: str, request: WorkflowRequest):
        """Execute custom workflow using LangGraph"""
        try:
            task_data = self.active_tasks[task_id]
            task_data["status"] = "running"
            task_data["updated_at"] = datetime.now()

            self.log_info(f"Starting custom workflow: {task_id}")

            # Prepare workflow input
            workflow_input = {
                "task_id": task_id,
                "user_id": request.user_id,
                "task_type": "custom_workflow",
                "workflow_name": request.workflow_name,
                "workflow_steps": request.workflow_steps,
                "input_data": request.input_data,
                "role_assignments": request.role_assignments or {}
            }

            start_time = datetime.now()

            try:
                # Execute browser orchestration workflow
                orchestrator = BrowserOrchestrator()
                result = await orchestrator.execute_task(
                    task_description=request.task_description,
                    user_config={"user_id": request.user_id},
                    workflow_type=request.workflow_type,
                    max_roles=request.max_roles
                )

                execution_time = (datetime.now() - start_time).total_seconds()

                # Update task with success
                task_data["status"] = "completed"
                task_data["result"] = result
                task_data["execution_time"] = execution_time
                task_data["progress"] = 100.0
                task_data["updated_at"] = datetime.now()

                # Consume quota
                await self._consume_task_quota(request.user_id, "custom_workflows", 1, task_id)
                await self._consume_task_quota(request.user_id, "browsing_tasks", len(request.workflow_steps), task_id)

                self.log_info(f"Custom workflow completed: {task_id}", execution_time=execution_time)

            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()

                # Handle error with fallback
                error_result = await self._handle_task_error(task_id, e, request)

                task_data["status"] = "failed"
                task_data["error"] = str(e)
                task_data["execution_time"] = execution_time
                task_data["error_details"] = error_result
                task_data["updated_at"] = datetime.now()

                self.log_error(f"Custom workflow failed: {task_id}", error=str(e))

        except Exception as e:
            self.log_error(f"Custom workflow execution failed: {e}")
            task_data["status"] = "failed"
            task_data["error"] = f"Workflow execution failed: {e}"
            task_data["updated_at"] = datetime.now()

    async def _execute_bulk_tasks(self, bulk_task_id: str, request: BulkBrowserTaskRequest):
        """Execute bulk tasks with parallel or sequential processing"""
        try:
            bulk_task = self.active_tasks[bulk_task_id]
            bulk_task["status"] = "running"
            bulk_task["updated_at"] = datetime.now()

            self.log_info(f"Starting bulk tasks: {bulk_task_id}, count: {len(request.tasks)}")

            if request.parallel_execution:
                await self._execute_parallel_bulk_tasks(bulk_task_id, request)
            else:
                await self._execute_sequential_bulk_tasks(bulk_task_id, request)

        except Exception as e:
            self.log_error(f"Bulk task execution failed: {e}")
            bulk_task = self.active_tasks[bulk_task_id]
            bulk_task["status"] = "failed"
            bulk_task["error"] = f"Bulk execution failed: {e}"
            bulk_task["updated_at"] = datetime.now()

    async def _execute_parallel_bulk_tasks(self, bulk_task_id: str, request: BulkBrowserTaskRequest):
        """Execute bulk tasks in parallel"""
        try:
            bulk_task = self.active_tasks[bulk_task_id]
            task_ids = bulk_task["task_ids"]

            # Process tasks in batches
            batch_size = min(request.max_parallel, request.batch_size)

            for i in range(0, len(task_ids), batch_size):
                batch_task_ids = task_ids[i:i + batch_size]

                # Create coroutines for batch
                batch_coroutines = []
                for task_id in batch_task_ids:
                    task_data = request.tasks[task_ids.index(task_id)]
                    coroutine = self._execute_individual_bulk_task(task_id, task_data, request.user_id)
                    batch_coroutines.append(coroutine)

                # Execute batch in parallel
                await asyncio.gather(*batch_coroutines, return_exceptions=True)

                # Update bulk task progress
                completed_tasks = sum(1 for tid in task_ids[:i + batch_size]
                                    if self.active_tasks[tid]["status"] in ["completed", "failed"])
                bulk_task["completed_tasks"] = completed_tasks
                bulk_task["progress"] = (completed_tasks / len(task_ids)) * 100
                bulk_task["updated_at"] = datetime.now()

                self.log_info(f"Bulk task batch completed: {i + batch_size}/{len(task_ids)}")

            # Update final status
            failed_tasks = sum(1 for tid in task_ids if self.active_tasks[tid]["status"] == "failed")
            bulk_task["failed_tasks"] = failed_tasks
            bulk_task["status"] = "completed" if failed_tasks == 0 else "partial_failure"
            bulk_task["progress"] = 100.0
            bulk_task["updated_at"] = datetime.now()

            self.log_info(f"Parallel bulk tasks completed: {bulk_task_id}")

        except Exception as e:
            self.log_error(f"Parallel bulk execution failed: {e}")
            bulk_task["status"] = "failed"
            bulk_task["error"] = str(e)

    async def _execute_sequential_bulk_tasks(self, bulk_task_id: str, request: BulkBrowserTaskRequest):
        """Execute bulk tasks sequentially"""
        try:
            bulk_task = self.active_tasks[bulk_task_id]
            task_ids = bulk_task["task_ids"]

            completed_tasks = 0
            failed_tasks = 0

            for i, task_id in enumerate(task_ids):
                task_data = request.tasks[i]

                try:
                    await self._execute_individual_bulk_task(task_id, task_data, request.user_id)

                    if self.active_tasks[task_id]["status"] == "completed":
                        completed_tasks += 1
                    else:
                        failed_tasks += 1

                        # Stop if continue_on_error is False
                        if not request.continue_on_error:
                            break

                except Exception as e:
                    failed_tasks += 1
                    self.log_warning(f"Individual bulk task failed: {task_id}, error: {e}")

                    if not request.continue_on_error:
                        break

                # Update progress
                bulk_task["completed_tasks"] = completed_tasks
                bulk_task["failed_tasks"] = failed_tasks
                bulk_task["progress"] = ((completed_tasks + failed_tasks) / len(task_ids)) * 100
                bulk_task["updated_at"] = datetime.now()

            # Update final status
            bulk_task["status"] = "completed" if failed_tasks == 0 else "partial_failure"
            bulk_task["progress"] = 100.0
            bulk_task["updated_at"] = datetime.now()

            self.log_info(f"Sequential bulk tasks completed: {bulk_task_id}")

        except Exception as e:
            self.log_error(f"Sequential bulk execution failed: {e}")
            bulk_task["status"] = "failed"
            bulk_task["error"] = str(e)

    async def _execute_individual_bulk_task(self, task_id: str, task_data: Dict[str, Any], user_id: str):
        """Execute an individual task within a bulk operation"""
        try:
            task = self.active_tasks[task_id]
            task["status"] = "running"
            task["updated_at"] = datetime.now()

            # Determine task type and execute accordingly
            task_type = task_data.get("task_type", "navigation")

            # Prepare workflow input
            workflow_input = {
                "task_id": task_id,
                "user_id": user_id,
                "task_type": task_type,
                "task_description": task_data.get("task_description", ""),
                "target_url": task_data.get("target_url"),
                "workflow_type": task_data.get("workflow_type", "sequential"),
                "max_roles": task_data.get("max_roles", 3),
                "timeout_seconds": task_data.get("timeout_seconds", 300),
                "verify_results": task_data.get("verify_results", True),
                "save_screenshots": task_data.get("save_screenshots", False),
                "custom_config": task_data.get("custom_config", {})
            }

            start_time = datetime.now()

            # Execute workflow
            orchestrator = BrowserOrchestrator()
            result = await orchestrator.execute_task(
                task_description=f"Bulk {task_type} operation",
                user_config={"user_id": user_id},
                workflow_type="parallel",
                max_roles=5
            )

            execution_time = (datetime.now() - start_time).total_seconds()

            # Update task with success
            task["status"] = "completed"
            task["result"] = result
            task["execution_time"] = execution_time
            task["progress"] = 100.0
            task["updated_at"] = datetime.now()

            # Consume quota
            await self._consume_task_quota(user_id, "browsing_tasks", 1, task_id)

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            task["status"] = "failed"
            task["error"] = str(e)
            task["execution_time"] = execution_time
            task["updated_at"] = datetime.now()

            self.log_error(f"Individual bulk task failed: {task_id}, error: {e}")

    async def _handle_task_error(self, task_id: str, error: Exception, request) -> Dict[str, Any]:
        """Handle task error with fallback mechanisms"""
        try:
            from app.services.error_handler import ErrorContext

            # Create error context
            error_context = ErrorContext(
                agent_id="browser_automation_api",
                task_id=task_id,
                workflow_id=f"workflow_{task_id}",
                user_id=request.user_id,
                operation="browser_task_execution"
            )

            # Handle error with error handler
            error_result = await error_handler.handle_error(
                error=error,
                context=error_context
            )

            # Try fallback if available
            if error_result.get("recovery_strategy", {}).get("use_fallback", False):
                fallback_result = await self._try_task_fallback(task_id, error, request)
                error_result["fallback_result"] = fallback_result

            return error_result

        except Exception as e:
            self.log_error(f"Error handling failed: {e}")
            return {
                "error_classification": {"category": "unknown", "severity": "high"},
                "recovery_strategy": {"type": "manual"},
                "recovery_result": {"success": False, "error": str(e)}
            }

    async def _try_task_fallback(self, task_id: str, error: Exception, request) -> Dict[str, Any]:
        """Try fallback mechanisms for failed task"""
        try:
            # Evaluate fallback triggers
            fallback_triggers = await fallback_manager.evaluate_fallback_triggers(
                agent_id="browser_automation",
                current_context={
                    "task_id": task_id,
                    "error": str(error),
                    "task_type": getattr(request, 'task_description', 'unknown')
                }
            )

            if fallback_triggers:
                # Execute first fallback recommendation
                fallback_result = await fallback_manager.execute_fallback(
                    fallback_recommendation=fallback_triggers[0],
                    current_agent="browser_automation",
                    context={"task_id": task_id, "user_id": request.user_id}
                )

                return fallback_result

            return {"success": False, "reason": "No fallback triggers available"}

        except Exception as e:
            self.log_error(f"Task fallback failed: {e}")
            return {"success": False, "error": str(e)}

    async def _consume_task_quota(self, user_id: str, quota_type: str, amount: int, task_id: str):
        """Consume quota for completed task"""
        try:
            from app.services.access_control import QuotaType

            quota_type_enum = QuotaType(quota_type)

            consumption_result = await access_control_manager.consume_quota(
                user_id=user_id,
                quota_type=quota_type_enum,
                amount=amount,
                metadata={
                    "task_id": task_id,
                    "source": "browser_automation_api",
                    "timestamp": datetime.now().isoformat()
                }
            )

            if not consumption_result["success"]:
                self.log_warning(f"Quota consumption failed: {consumption_result['reason']}")

        except Exception as e:
            self.log_error(f"Quota consumption failed: {e}")

    async def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """Clean up old completed tasks"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            tasks_to_remove = []

            for task_id, task_data in self.active_tasks.items():
                if (task_data["status"] in ["completed", "failed", "cancelled"] and
                    task_data.get("updated_at", task_data["created_at"]) < cutoff_time):
                    tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del self.active_tasks[task_id]

            if tasks_to_remove:
                self.log_info(f"Cleaned up {len(tasks_to_remove)} old tasks")

        except Exception as e:
            self.log_error(f"Task cleanup failed: {e}")


# Create router instance
browser_automation_api = BrowserAutomationAPI()
router = browser_automation_api.router
