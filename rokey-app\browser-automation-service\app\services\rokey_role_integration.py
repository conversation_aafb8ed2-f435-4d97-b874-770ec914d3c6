"""
RouKey Role Integration Service
Connects LangGraph workflow with Rou<PERSON>ey's role classification and routing strategies
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import httpx

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import BrowserAutomationException


class RoutingStrategy(Enum):
    """RouKey routing strategies"""
    COMPLEX_ROUTING = "complex_routing"
    STRICT_FALLBACK = "strict_fallback"
    AB_TESTING = "ab_testing"
    COST_OPTIMIZED = "cost_optimized"
    SEQUENTIAL = "sequential"
    SUPERVISOR = "supervisor"


class TaskComplexity(Enum):
    """Task complexity levels for routing decisions"""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    EXPERT = "expert"


class RouKeyRoleIntegration(LoggerMixin):
    """
    Integration service for RouKey role classification and routing

    Features:
    - Dynamic role classification for browser tasks
    - Integration with RouKey's routing strategies
    - Cost-optimized model selection
    - A/B testing support for routing decisions
    - Fallback mechanism integration
    - Real-time role consultation
    - Performance tracking for routing decisions
    """

    def __init__(self):
        self.rokey_api_base = settings.ROKEY_API_BASE_URL
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Routing strategy cache
        self.routing_cache: Dict[str, Dict[str, Any]] = {}
        self.role_assignments: Dict[str, List[str]] = {}

        # Performance tracking
        self.routing_metrics = {
            "total_classifications": 0,
            "successful_routings": 0,
            "fallback_activations": 0,
            "cost_savings": 0.0,
            "average_response_time": 0.0
        }

        # REMOVED: No keyword-based classification patterns - all classification uses RouKey's Gemini API
        # All task analysis and role routing now handled by RouKey's main classification system

        self.log_info("RouKey role integration initialized")

    async def initialize(self):
        """Initialize the role integration service"""
        try:
            # Test connection to RouKey API
            await self._test_rokey_connection()

            # Load user configurations and role assignments
            await self._load_role_configurations()

            self.log_info("RouKey role integration service started")

        except Exception as e:
            self.log_error(f"Failed to initialize RouKey role integration: {e}")
            raise BrowserAutomationException(f"Role integration initialization failed: {e}")

    async def classify_browser_task(
        self,
        task_description: str,
        context: Dict[str, Any] = None,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Classify a browser automation task and determine appropriate roles

        Args:
            task_description: Description of the browser task
            context: Additional context about the task
            user_id: User ID for personalized routing

        Returns:
            Dictionary containing classification results and role recommendations
        """
        try:
            self.log_info("Classifying browser task", task_length=len(task_description))

            # Analyze task complexity and type
            task_analysis = await self._analyze_task_complexity(task_description, context)

            # Get role recommendations based on task type
            role_recommendations = await self._get_role_recommendations(
                task_analysis, user_id
            )

            # Apply RouKey routing strategy
            routing_decision = await self._apply_routing_strategy(
                task_analysis, role_recommendations, user_id
            )

            # Update metrics
            self.routing_metrics["total_classifications"] += 1

            classification_result = {
                "task_type": task_analysis["task_type"],
                "complexity": task_analysis["complexity"].value,
                "confidence": task_analysis["confidence"],
                "recommended_roles": role_recommendations,
                "routing_decision": routing_decision,
                "routing_strategy": routing_decision.get("strategy"),
                "selected_role": routing_decision.get("selected_role"),
                "fallback_roles": routing_decision.get("fallback_roles", []),
                "cost_estimate": routing_decision.get("cost_estimate"),
                "timestamp": datetime.now().isoformat()
            }

            self.log_info(
                "Task classification completed",
                task_type=task_analysis["task_type"],
                selected_role=routing_decision.get("selected_role")
            )

            return classification_result

        except Exception as e:
            self.log_error(f"Task classification failed: {e}")
            raise BrowserAutomationException(f"Task classification failed: {e}")

    async def get_role_configuration(
        self,
        role: str,
        user_id: str,
        routing_strategy: RoutingStrategy = RoutingStrategy.COMPLEX_ROUTING
    ) -> Dict[str, Any]:
        """
        Get LLM configuration for a specific role using RouKey's routing

        Args:
            role: Role name to get configuration for
            user_id: User ID for personalized configuration
            routing_strategy: Routing strategy to apply

        Returns:
            Dictionary containing LLM configuration for the role
        """
        try:
            # Check cache first
            cache_key = f"{user_id}:{role}:{routing_strategy.value}"
            if cache_key in self.routing_cache:
                cached_config = self.routing_cache[cache_key]
                if self._is_cache_valid(cached_config):
                    return cached_config["config"]

            # Get configuration from RouKey API
            config = await self._fetch_role_configuration(role, user_id, routing_strategy)

            # Cache the configuration
            self.routing_cache[cache_key] = {
                "config": config,
                "timestamp": datetime.now(),
                "ttl": 300  # 5 minutes
            }

            self.log_info(f"Role configuration retrieved: {role}", strategy=routing_strategy.value)

            return config

        except Exception as e:
            self.log_error(f"Failed to get role configuration: {e}")
            raise BrowserAutomationException(f"Role configuration retrieval failed: {e}")

    async def execute_with_routing_strategy(
        self,
        task_description: str,
        user_config: Dict[str, Any],
        routing_strategy: RoutingStrategy,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute task with specific RouKey routing strategy

        Args:
            task_description: Task to execute
            user_config: User's configuration
            routing_strategy: Routing strategy to use
            context: Additional execution context

        Returns:
            Dictionary containing execution results and routing information
        """
        try:
            user_id = user_config.get("user_id")

            # Classify the task
            classification = await self.classify_browser_task(
                task_description, context, user_id
            )

            # Execute based on routing strategy
            if routing_strategy == RoutingStrategy.COMPLEX_ROUTING:
                result = await self._execute_complex_routing(
                    task_description, classification, user_config, context
                )
            elif routing_strategy == RoutingStrategy.STRICT_FALLBACK:
                result = await self._execute_strict_fallback(
                    task_description, classification, user_config, context
                )
            elif routing_strategy == RoutingStrategy.AB_TESTING:
                result = await self._execute_ab_testing(
                    task_description, classification, user_config, context
                )
            elif routing_strategy == RoutingStrategy.COST_OPTIMIZED:
                result = await self._execute_cost_optimized(
                    task_description, classification, user_config, context
                )
            elif routing_strategy == RoutingStrategy.SEQUENTIAL:
                result = await self._execute_sequential(
                    task_description, classification, user_config, context
                )
            elif routing_strategy == RoutingStrategy.SUPERVISOR:
                result = await self._execute_supervisor(
                    task_description, classification, user_config, context
                )
            else:
                raise BrowserAutomationException(f"Unknown routing strategy: {routing_strategy}")

            # Update success metrics
            if result.get("success", False):
                self.routing_metrics["successful_routings"] += 1

            return {
                "classification": classification,
                "routing_strategy": routing_strategy.value,
                "execution_result": result,
                "routing_metrics": self._get_routing_metrics()
            }

        except Exception as e:
            self.log_error(f"Routing strategy execution failed: {e}")
            raise BrowserAutomationException(f"Routing execution failed: {e}")

    async def handle_dynamic_role_consultation(
        self,
        current_role: str,
        problem_description: str,
        user_config: Dict[str, Any],
        available_roles: List[str] = None
    ) -> Dict[str, Any]:
        """
        Handle dynamic role consultation when current role faces problems

        Args:
            current_role: Role currently handling the task
            problem_description: Description of the problem encountered
            user_config: User's configuration
            available_roles: List of available roles for consultation

        Returns:
            Dictionary containing consultation results and recommendations
        """
        try:
            user_id = user_config.get("user_id")

            self.log_info(
                "Handling dynamic role consultation",
                current_role=current_role,
                problem=problem_description[:100]
            )

            # Analyze the problem and determine best consulting role
            consultation_analysis = await self._analyze_consultation_need(
                current_role, problem_description, user_config
            )

            # Get available roles for this user
            if not available_roles:
                available_roles = await self._get_user_available_roles(user_id)

            # Filter roles relevant to the problem
            relevant_roles = await self._filter_relevant_roles(
                problem_description, available_roles, current_role
            )

            # Select best consulting role based on routing strategy
            consulting_role = await self._select_consulting_role(
                consultation_analysis, relevant_roles, user_config
            )

            # Get configuration for consulting role
            consulting_config = await self.get_role_configuration(
                consulting_role, user_id
            )

            consultation_result = {
                "current_role": current_role,
                "problem_analysis": consultation_analysis,
                "consulting_role": consulting_role,
                "consulting_config": consulting_config,
                "consultation_strategy": consultation_analysis.get("strategy"),
                "estimated_cost": consultation_analysis.get("cost_estimate"),
                "confidence": consultation_analysis.get("confidence"),
                "timestamp": datetime.now().isoformat()
            }

            self.log_info(
                "Dynamic role consultation completed",
                consulting_role=consulting_role,
                strategy=consultation_analysis.get("strategy")
            )

            return consultation_result

        except Exception as e:
            self.log_error(f"Dynamic role consultation failed: {e}")
            raise BrowserAutomationException(f"Role consultation failed: {e}")

    async def optimize_role_assignment(
        self,
        workflow_tasks: List[Dict[str, Any]],
        user_config: Dict[str, Any],
        optimization_criteria: str = "cost_performance"
    ) -> Dict[str, Any]:
        """
        Optimize role assignments for a complete workflow

        Args:
            workflow_tasks: List of tasks in the workflow
            user_config: User's configuration
            optimization_criteria: Criteria for optimization (cost, performance, speed)

        Returns:
            Dictionary containing optimized role assignments
        """
        try:
            user_id = user_config.get("user_id")

            self.log_info(
                "Optimizing role assignments",
                task_count=len(workflow_tasks),
                criteria=optimization_criteria
            )

            # Analyze all tasks and their requirements
            task_analyses = []
            for task in workflow_tasks:
                analysis = await self._analyze_task_complexity(
                    task.get("description", ""), task.get("context", {})
                )
                task_analyses.append({
                    "task": task,
                    "analysis": analysis
                })

            # Get available roles and their capabilities
            available_roles = await self._get_user_available_roles(user_id)
            role_capabilities = await self._get_role_capabilities(available_roles, user_id)

            # Optimize assignments based on criteria
            if optimization_criteria == "cost_performance":
                assignments = await self._optimize_cost_performance(
                    task_analyses, role_capabilities, user_config
                )
            elif optimization_criteria == "speed":
                assignments = await self._optimize_speed(
                    task_analyses, role_capabilities, user_config
                )
            elif optimization_criteria == "accuracy":
                assignments = await self._optimize_accuracy(
                    task_analyses, role_capabilities, user_config
                )
            else:
                assignments = await self._optimize_balanced(
                    task_analyses, role_capabilities, user_config
                )

            # Calculate optimization metrics
            optimization_metrics = await self._calculate_optimization_metrics(
                assignments, task_analyses, role_capabilities
            )

            optimization_result = {
                "workflow_id": workflow_tasks[0].get("workflow_id") if workflow_tasks else None,
                "optimization_criteria": optimization_criteria,
                "task_assignments": assignments,
                "optimization_metrics": optimization_metrics,
                "estimated_total_cost": optimization_metrics.get("total_cost"),
                "estimated_duration": optimization_metrics.get("total_duration"),
                "confidence_score": optimization_metrics.get("confidence"),
                "timestamp": datetime.now().isoformat()
            }

            self.log_info(
                "Role assignment optimization completed",
                total_cost=optimization_metrics.get("total_cost"),
                duration=optimization_metrics.get("total_duration")
            )

            return optimization_result

        except Exception as e:
            self.log_error(f"Role assignment optimization failed: {e}")
            raise BrowserAutomationException(f"Role optimization failed: {e}")

    async def get_routing_analytics(
        self,
        user_id: str = None,
        time_period: str = "24h"
    ) -> Dict[str, Any]:
        """
        Get analytics about routing decisions and performance

        Args:
            user_id: Specific user to analyze (None for global)
            time_period: Time period for analysis

        Returns:
            Dictionary containing routing analytics
        """
        try:
            analytics = {
                "routing_metrics": self.routing_metrics.copy(),
                "cache_statistics": {
                    "total_cached_configs": len(self.routing_cache),
                    "cache_hit_rate": self._calculate_cache_hit_rate()
                },
                "role_usage": await self._get_role_usage_statistics(user_id, time_period),
                "strategy_performance": await self._get_strategy_performance(user_id, time_period),
                "cost_analysis": await self._get_cost_analysis(user_id, time_period),
                "optimization_opportunities": await self._identify_optimization_opportunities(user_id)
            }

            return analytics

        except Exception as e:
            self.log_error(f"Failed to get routing analytics: {e}")
            return {"error": str(e)}

    async def _test_rokey_connection(self):
        """Test connection to RouKey API"""
        try:
            response = await self.http_client.get(f"{self.rokey_api_base}/health")
            if response.status_code != 200:
                raise BrowserAutomationException("RouKey API health check failed")

            self.log_info("RouKey API connection verified")

        except Exception as e:
            self.log_warning(f"RouKey API connection test failed: {e}")
            # Don't raise exception - service can work in degraded mode

    async def _load_role_configurations(self):
        """Load role configurations and assignments"""
        try:
            # This would typically load from RouKey's configuration API
            # For now, we'll use default configurations

            default_roles = [
                "web_navigator", "data_extractor", "form_filler", "verification_agent",
                "research_assistant", "shopping_assistant", "price_comparison",
                "fact_checker", "web_automation", "task_executor"
            ]

            for role in default_roles:
                if role not in self.role_assignments:
                    self.role_assignments[role] = []

            self.log_info(f"Loaded {len(default_roles)} default role configurations")

        except Exception as e:
            self.log_error(f"Failed to load role configurations: {e}")

    async def _analyze_task_complexity(
        self,
        task_description: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        REMOVED: Task complexity analysis now handled by RouKey's Gemini classification
        This method should not be used - all analysis goes through RouKey API
        """
        raise Exception("Task complexity analysis removed - use RouKey's Gemini classification API instead")

    async def _get_role_recommendations(
        self,
        task_analysis: Dict[str, Any],
        user_id: str = None
    ) -> List[str]:
        """
        REMOVED: Role recommendations now handled by RouKey's Gemini classification
        This method should not be used - all role selection goes through RouKey API
        """
        raise Exception("Role recommendations removed - use RouKey's Gemini classification API instead")

    async def _apply_routing_strategy(
        self,
        task_analysis: Dict[str, Any],
        role_recommendations: List[str],
        user_id: str = None
    ) -> Dict[str, Any]:
        """Apply RouKey routing strategy to select the best role"""
        try:
            # Get user's preferred routing strategy
            user_strategy = await self._get_user_routing_strategy(user_id)

            if user_strategy == RoutingStrategy.COST_OPTIMIZED:
                return await self._apply_cost_optimized_routing(
                    task_analysis, role_recommendations, user_id
                )
            elif user_strategy == RoutingStrategy.AB_TESTING:
                return await self._apply_ab_testing_routing(
                    task_analysis, role_recommendations, user_id
                )
            else:
                # Default complex routing
                return await self._apply_complex_routing(
                    task_analysis, role_recommendations, user_id
                )

        except Exception as e:
            self.log_error(f"Routing strategy application failed: {e}")
            return {
                "strategy": "fallback",
                "selected_role": role_recommendations[0] if role_recommendations else "web_automation",
                "fallback_roles": role_recommendations[1:] if len(role_recommendations) > 1 else [],
                "cost_estimate": 0.01
            }

    async def _fetch_role_configuration(
        self,
        role: str,
        user_id: str,
        routing_strategy: RoutingStrategy
    ) -> Dict[str, Any]:
        """Fetch role configuration from RouKey API"""
        try:
            # This would make an actual API call to RouKey
            # For now, return a mock configuration

            base_config = {
                "provider": "openai",
                "model": "gpt-4o",
                "temperature": 0.7,
                "max_tokens": 2000,
                "role": role,
                "routing_strategy": routing_strategy.value
            }

            # Adjust configuration based on role and strategy
            if routing_strategy == RoutingStrategy.COST_OPTIMIZED:
                if role in ["web_navigator", "form_filler"]:
                    base_config.update({
                        "model": "gpt-3.5-turbo",
                        "max_tokens": 1000
                    })

            return base_config

        except Exception as e:
            self.log_error(f"Failed to fetch role configuration: {e}")
            raise BrowserAutomationException(f"Role configuration fetch failed: {e}")

    async def _execute_complex_routing(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using complex routing strategy"""
        try:
            selected_role = classification["selected_role"]

            # Get role configuration
            role_config = await self.get_role_configuration(
                selected_role, user_config.get("user_id"), RoutingStrategy.COMPLEX_ROUTING
            )

            # Execute with primary role
            result = await self._execute_with_role(
                task_description, selected_role, role_config, context
            )

            # If primary role fails, try fallback roles
            if not result.get("success", False) and classification.get("fallback_roles"):
                for fallback_role in classification["fallback_roles"]:
                    try:
                        fallback_config = await self.get_role_configuration(
                            fallback_role, user_config.get("user_id"), RoutingStrategy.STRICT_FALLBACK
                        )

                        fallback_result = await self._execute_with_role(
                            task_description, fallback_role, fallback_config, context
                        )

                        if fallback_result.get("success", False):
                            result = fallback_result
                            result["used_fallback"] = True
                            result["fallback_role"] = fallback_role
                            self.routing_metrics["fallback_activations"] += 1
                            break

                    except Exception as e:
                        self.log_warning(f"Fallback role {fallback_role} failed: {e}")
                        continue

            return result

        except Exception as e:
            self.log_error(f"Complex routing execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_strict_fallback(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using strict fallback strategy"""
        try:
            # Try roles in strict order until one succeeds
            all_roles = [classification["selected_role"]] + classification.get("fallback_roles", [])

            for role in all_roles:
                try:
                    role_config = await self.get_role_configuration(
                        role, user_config.get("user_id"), RoutingStrategy.STRICT_FALLBACK
                    )

                    result = await self._execute_with_role(
                        task_description, role, role_config, context
                    )

                    if result.get("success", False):
                        result["strategy"] = "strict_fallback"
                        result["selected_role"] = role
                        return result

                except Exception as e:
                    self.log_warning(f"Role {role} failed in strict fallback: {e}")
                    continue

            return {"success": False, "error": "All roles failed in strict fallback"}

        except Exception as e:
            self.log_error(f"Strict fallback execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_ab_testing(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using A/B testing strategy"""
        try:
            # Select role based on A/B testing configuration
            ab_config = await self._get_ab_testing_config(user_config.get("user_id"))

            selected_role = await self._select_ab_testing_role(
                classification, ab_config
            )

            role_config = await self.get_role_configuration(
                selected_role, user_config.get("user_id"), RoutingStrategy.AB_TESTING
            )

            result = await self._execute_with_role(
                task_description, selected_role, role_config, context
            )

            # Record A/B testing metrics
            await self._record_ab_testing_result(
                ab_config, selected_role, result, user_config.get("user_id")
            )

            result["strategy"] = "ab_testing"
            result["ab_variant"] = ab_config.get("variant")

            return result

        except Exception as e:
            self.log_error(f"A/B testing execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_cost_optimized(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using cost-optimized strategy"""
        try:
            # Select most cost-effective role for the task complexity
            complexity = classification.get("complexity", "simple")

            if complexity == TaskComplexity.SIMPLE.value:
                # Use cheaper models for simple tasks
                cost_optimized_role = await self._select_cost_optimized_role(
                    classification["recommended_roles"], "cheap"
                )
            else:
                # Use balanced approach for complex tasks
                cost_optimized_role = await self._select_cost_optimized_role(
                    classification["recommended_roles"], "balanced"
                )

            role_config = await self.get_role_configuration(
                cost_optimized_role, user_config.get("user_id"), RoutingStrategy.COST_OPTIMIZED
            )

            result = await self._execute_with_role(
                task_description, cost_optimized_role, role_config, context
            )

            # Calculate cost savings
            standard_cost = await self._calculate_standard_cost(classification)
            optimized_cost = result.get("cost", 0)
            cost_savings = max(0, standard_cost - optimized_cost)

            self.routing_metrics["cost_savings"] += cost_savings

            result["strategy"] = "cost_optimized"
            result["cost_savings"] = cost_savings

            return result

        except Exception as e:
            self.log_error(f"Cost-optimized execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_sequential(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using sequential strategy (single role)"""
        try:
            selected_role = classification["selected_role"]

            role_config = await self.get_role_configuration(
                selected_role, user_config.get("user_id"), RoutingStrategy.SEQUENTIAL
            )

            result = await self._execute_with_role(
                task_description, selected_role, role_config, context
            )

            result["strategy"] = "sequential"

            return result

        except Exception as e:
            self.log_error(f"Sequential execution failed: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_supervisor(
        self,
        task_description: str,
        classification: Dict[str, Any],
        user_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task using supervisor strategy (hierarchical)"""
        try:
            # Use supervisor agent to coordinate multiple roles
            supervisor_config = await self.get_role_configuration(
                "supervisor", user_config.get("user_id"), RoutingStrategy.SUPERVISOR
            )

            # Create execution plan with multiple roles
            execution_plan = await self._create_supervisor_execution_plan(
                task_description, classification, user_config
            )

            result = await self._execute_supervisor_plan(
                execution_plan, supervisor_config, context
            )

            result["strategy"] = "supervisor"
            result["execution_plan"] = execution_plan

            return result

        except Exception as e:
            self.log_error(f"Supervisor execution failed: {e}")
            return {"success": False, "error": str(e)}

    # Helper methods for routing strategies and analysis
    async def _get_user_routing_strategy(self, user_id: str = None) -> RoutingStrategy:
        """Get user's preferred routing strategy"""
        try:
            if not user_id:
                return RoutingStrategy.COMPLEX_ROUTING

            # This would fetch from RouKey API
            # For now, return default
            return RoutingStrategy.COMPLEX_ROUTING

        except Exception:
            return RoutingStrategy.COMPLEX_ROUTING

    async def _apply_complex_routing(
        self,
        task_analysis: Dict[str, Any],
        role_recommendations: List[str],
        user_id: str = None
    ) -> Dict[str, Any]:
        """Apply complex routing logic"""
        try:
            # Select best role based on task complexity and user preferences
            complexity = task_analysis.get("complexity", TaskComplexity.SIMPLE)

            if complexity == TaskComplexity.EXPERT:
                # Use most capable role for expert tasks
                selected_role = role_recommendations[0] if role_recommendations else "expert_assistant"
            elif complexity == TaskComplexity.COMPLEX:
                # Use specialized role for complex tasks
                selected_role = role_recommendations[0] if role_recommendations else "research_assistant"
            else:
                # Use efficient role for simple/moderate tasks
                selected_role = role_recommendations[0] if role_recommendations else "web_automation"

            return {
                "strategy": "complex_routing",
                "selected_role": selected_role,
                "fallback_roles": role_recommendations[1:3] if len(role_recommendations) > 1 else [],
                "cost_estimate": await self._estimate_role_cost(selected_role, task_analysis),
                "confidence": task_analysis.get("confidence", 0.8)
            }

        except Exception as e:
            self.log_error(f"Complex routing application failed: {e}")
            return {
                "strategy": "fallback",
                "selected_role": "web_automation",
                "fallback_roles": [],
                "cost_estimate": 0.01
            }

    async def _apply_cost_optimized_routing(
        self,
        task_analysis: Dict[str, Any],
        role_recommendations: List[str],
        user_id: str = None
    ) -> Dict[str, Any]:
        """Apply cost-optimized routing logic"""
        try:
            # Select cheapest suitable role
            complexity = task_analysis.get("complexity", TaskComplexity.SIMPLE)

            if complexity in [TaskComplexity.SIMPLE, TaskComplexity.MODERATE]:
                # Use cheaper models for simple tasks
                cost_roles = ["web_navigator", "form_filler", "basic_automation"]
                selected_role = next((role for role in cost_roles if role in role_recommendations),
                                   role_recommendations[0] if role_recommendations else "web_automation")
            else:
                # Use balanced approach for complex tasks
                selected_role = role_recommendations[0] if role_recommendations else "research_assistant"

            return {
                "strategy": "cost_optimized",
                "selected_role": selected_role,
                "fallback_roles": role_recommendations[1:2] if len(role_recommendations) > 1 else [],
                "cost_estimate": await self._estimate_role_cost(selected_role, task_analysis) * 0.7,  # 30% savings
                "optimization": "cost"
            }

        except Exception as e:
            self.log_error(f"Cost-optimized routing failed: {e}")
            return {
                "strategy": "fallback",
                "selected_role": "web_automation",
                "cost_estimate": 0.01
            }

    async def _apply_ab_testing_routing(
        self,
        task_analysis: Dict[str, Any],
        role_recommendations: List[str],
        user_id: str = None
    ) -> Dict[str, Any]:
        """Apply A/B testing routing logic"""
        try:
            # Simple A/B test: alternate between top 2 roles
            import random

            if len(role_recommendations) >= 2:
                # 50/50 split between top 2 roles
                selected_role = random.choice(role_recommendations[:2])
                variant = "A" if selected_role == role_recommendations[0] else "B"
            else:
                selected_role = role_recommendations[0] if role_recommendations else "web_automation"
                variant = "A"

            return {
                "strategy": "ab_testing",
                "selected_role": selected_role,
                "fallback_roles": [r for r in role_recommendations if r != selected_role][:2],
                "cost_estimate": await self._estimate_role_cost(selected_role, task_analysis),
                "ab_variant": variant
            }

        except Exception as e:
            self.log_error(f"A/B testing routing failed: {e}")
            return {
                "strategy": "fallback",
                "selected_role": "web_automation",
                "cost_estimate": 0.01
            }

    async def _execute_with_role(
        self,
        task_description: str,
        role: str,
        role_config: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute task with specific role configuration"""
        try:
            # This would integrate with the actual LangGraph execution
            # For now, return a mock successful result

            execution_result = {
                "success": True,
                "role": role,
                "task": task_description,
                "result": f"Task executed successfully with {role}",
                "cost": await self._calculate_execution_cost(role_config),
                "execution_time": 2.5,
                "timestamp": datetime.now().isoformat()
            }

            return execution_result

        except Exception as e:
            self.log_error(f"Role execution failed: {e}")
            return {
                "success": False,
                "role": role,
                "error": str(e),
                "cost": 0,
                "execution_time": 0
            }

    async def _get_user_available_roles(self, user_id: str) -> List[str]:
        """Get roles available to a specific user"""
        try:
            # This would fetch from RouKey's user configuration
            # For now, return default roles based on subscription tier

            default_roles = [
                "web_navigator", "data_extractor", "form_filler", "verification_agent",
                "research_assistant", "shopping_assistant", "web_automation", "task_executor"
            ]

            return default_roles

        except Exception as e:
            self.log_error(f"Failed to get user roles: {e}")
            return ["web_automation", "task_executor"]

    async def _estimate_role_cost(self, role: str, task_analysis: Dict[str, Any]) -> float:
        """Estimate cost for executing task with specific role"""
        try:
            # Base cost estimates (in USD)
            base_costs = {
                "web_navigator": 0.005,
                "data_extractor": 0.01,
                "form_filler": 0.007,
                "verification_agent": 0.015,
                "research_assistant": 0.02,
                "shopping_assistant": 0.012,
                "expert_assistant": 0.03,
                "web_automation": 0.008,
                "task_executor": 0.006
            }

            base_cost = base_costs.get(role, 0.01)

            # Adjust based on task complexity
            complexity = task_analysis.get("complexity", TaskComplexity.SIMPLE)
            if complexity == TaskComplexity.EXPERT:
                base_cost *= 3.0
            elif complexity == TaskComplexity.COMPLEX:
                base_cost *= 2.0
            elif complexity == TaskComplexity.MODERATE:
                base_cost *= 1.5

            return round(base_cost, 4)

        except Exception as e:
            self.log_error(f"Cost estimation failed: {e}")
            return 0.01

    async def _calculate_execution_cost(self, role_config: Dict[str, Any]) -> float:
        """Calculate actual execution cost based on role configuration"""
        try:
            # Simple cost calculation based on model and tokens
            model = role_config.get("model", "gpt-3.5-turbo")
            max_tokens = role_config.get("max_tokens", 1000)

            # Cost per 1K tokens (approximate)
            token_costs = {
                "gpt-4o": 0.03,
                "gpt-4": 0.03,
                "gpt-3.5-turbo": 0.002,
                "claude-3": 0.015,
                "gemini-pro": 0.001
            }

            cost_per_1k = token_costs.get(model, 0.002)
            estimated_cost = (max_tokens / 1000) * cost_per_1k

            return round(estimated_cost, 4)

        except Exception as e:
            self.log_error(f"Execution cost calculation failed: {e}")
            return 0.01

    async def _is_cache_valid(self, cached_config: Dict[str, Any]) -> bool:
        """Check if cached configuration is still valid"""
        try:
            cache_time = cached_config.get("timestamp")
            ttl = cached_config.get("ttl", 300)  # 5 minutes default

            if not cache_time:
                return False

            age = (datetime.now() - cache_time).total_seconds()
            return age < ttl

        except Exception:
            return False

    async def _get_routing_metrics(self) -> Dict[str, Any]:
        """Get current routing metrics"""
        try:
            metrics = self.routing_metrics.copy()

            # Calculate success rate
            if metrics["total_classifications"] > 0:
                metrics["success_rate"] = (metrics["successful_routings"] / metrics["total_classifications"]) * 100
            else:
                metrics["success_rate"] = 0.0

            # Calculate fallback rate
            if metrics["total_classifications"] > 0:
                metrics["fallback_rate"] = (metrics["fallback_activations"] / metrics["total_classifications"]) * 100
            else:
                metrics["fallback_rate"] = 0.0

            return metrics

        except Exception as e:
            self.log_error(f"Failed to get routing metrics: {e}")
            return {}

    async def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        try:
            # This would track actual cache hits vs misses
            # For now, return estimated rate
            return 0.75  # 75% hit rate

        except Exception:
            return 0.0

    # Placeholder methods for advanced features
    async def _analyze_consultation_need(self, current_role: str, problem: str, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze consultation need and determine strategy"""
        return {
            "strategy": "expert_consultation",
            "confidence": 0.8,
            "cost_estimate": 0.02
        }

    async def _filter_relevant_roles(self, problem: str, available_roles: List[str], current_role: str) -> List[str]:
        """Filter roles relevant to the problem"""
        return [role for role in available_roles if role != current_role][:3]

    async def _select_consulting_role(self, analysis: Dict[str, Any], relevant_roles: List[str], user_config: Dict[str, Any]) -> str:
        """Select best consulting role"""
        return relevant_roles[0] if relevant_roles else "expert_assistant"

    async def _optimize_cost_performance(self, task_analyses: List[Dict], role_capabilities: Dict, user_config: Dict) -> List[Dict]:
        """Optimize assignments for cost and performance"""
        assignments = []
        for task_analysis in task_analyses:
            assignments.append({
                "task_id": task_analysis["task"].get("id"),
                "assigned_role": "web_automation",
                "cost_estimate": 0.01,
                "confidence": 0.8
            })
        return assignments

    async def _optimize_speed(self, task_analyses: List[Dict], role_capabilities: Dict, user_config: Dict) -> List[Dict]:
        """Optimize assignments for speed"""
        return await self._optimize_cost_performance(task_analyses, role_capabilities, user_config)

    async def _optimize_accuracy(self, task_analyses: List[Dict], role_capabilities: Dict, user_config: Dict) -> List[Dict]:
        """Optimize assignments for accuracy"""
        return await self._optimize_cost_performance(task_analyses, role_capabilities, user_config)

    async def _optimize_balanced(self, task_analyses: List[Dict], role_capabilities: Dict, user_config: Dict) -> List[Dict]:
        """Optimize assignments with balanced approach"""
        return await self._optimize_cost_performance(task_analyses, role_capabilities, user_config)

    async def _get_role_capabilities(self, roles: List[str], user_id: str) -> Dict[str, Any]:
        """Get capabilities for each role"""
        return {role: {"cost": 0.01, "speed": 1.0, "accuracy": 0.8} for role in roles}

    async def _calculate_optimization_metrics(self, assignments: List[Dict], task_analyses: List[Dict], role_capabilities: Dict) -> Dict[str, Any]:
        """Calculate optimization metrics"""
        return {
            "total_cost": sum(a.get("cost_estimate", 0) for a in assignments),
            "total_duration": len(assignments) * 2.0,
            "confidence": 0.85
        }

    async def _get_role_usage_statistics(self, user_id: str, time_period: str) -> Dict[str, Any]:
        """Get role usage statistics"""
        return {"web_automation": 45, "data_extractor": 30, "verification_agent": 25}

    async def _get_strategy_performance(self, user_id: str, time_period: str) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        return {
            "complex_routing": {"success_rate": 92, "avg_cost": 0.015},
            "cost_optimized": {"success_rate": 88, "avg_cost": 0.008},
            "ab_testing": {"success_rate": 90, "avg_cost": 0.012}
        }

    async def _get_cost_analysis(self, user_id: str, time_period: str) -> Dict[str, Any]:
        """Get cost analysis"""
        return {
            "total_cost": 2.45,
            "cost_by_role": {"web_automation": 1.20, "data_extractor": 0.85, "verification_agent": 0.40},
            "cost_savings": 0.75
        }

    async def _identify_optimization_opportunities(self, user_id: str) -> List[Dict[str, Any]]:
        """Identify optimization opportunities"""
        return [
            {
                "type": "cost_optimization",
                "description": "Switch simple navigation tasks to cheaper models",
                "potential_savings": 0.25
            },
            {
                "type": "performance_optimization",
                "description": "Use specialized roles for data extraction",
                "potential_improvement": "15% faster execution"
            }
        ]

    async def cleanup(self):
        """Cleanup role integration resources"""
        try:
            await self.http_client.aclose()
            self.routing_cache.clear()
            self.role_assignments.clear()

            self.log_info("RouKey role integration cleanup completed")

        except Exception as e:
            self.log_error(f"Role integration cleanup failed: {e}")


# Global RouKey role integration instance
rokey_role_integration = RouKeyRoleIntegration()